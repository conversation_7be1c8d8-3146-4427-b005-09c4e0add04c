"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// import { EditStudentModal } from \"@/components/modals/edit-student-modal\" // Using unified AddStudentModal instead\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            // Prepare data for API - only send fields that can be updated\n            const updateData = {\n                first_name: updatedStudent.first_name,\n                last_name: updatedStudent.last_name,\n                email: updatedStudent.email,\n                phone: updatedStudent.phone,\n                date_of_birth: updatedStudent.date_of_birth,\n                gender: updatedStudent.gender,\n                address: updatedStudent.address,\n                blood_group: updatedStudent.blood_group,\n                nationality: updatedStudent.nationality,\n                religion: updatedStudent.religion,\n                category: updatedStudent.category,\n                mother_tongue: updatedStudent.mother_tongue,\n                previous_school: updatedStudent.previous_school,\n                medical_conditions: updatedStudent.medical_conditions,\n                emergency_contact_name: updatedStudent.emergency_contact_name,\n                emergency_contact_phone: updatedStudent.emergency_contact_phone,\n                emergency_contact_relation: updatedStudent.emergency_contact_relation,\n                admission_number: updatedStudent.admission_number,\n                admission_date: updatedStudent.admission_date,\n                roll_number: updatedStudent.roll_number,\n                transport_required: updatedStudent.transport_required,\n                hostel_required: updatedStudent.hostel_required,\n                status: updatedStudent.status,\n                current_class_id: updatedStudent.current_class_id\n            };\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.update(String(updatedStudent.id), updateData);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_name\",\n            header: \"Emergency Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const contact = row.getValue(\"emergency_contact_name\");\n                return contact || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_phone\",\n            header: \"Emergency Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const contactPhone = row.getValue(\"emergency_contact_phone\");\n                return contactPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 615,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 675,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 674,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 712,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 685,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});