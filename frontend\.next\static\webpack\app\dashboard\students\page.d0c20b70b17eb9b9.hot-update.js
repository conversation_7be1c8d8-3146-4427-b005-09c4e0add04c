"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// import { EditStudentModal } from \"@/components/modals/edit-student-modal\" // Using unified AddStudentModal instead\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            // Prepare data for API - only send fields that can be updated\n            const updateData = {\n                first_name: updatedStudent.first_name,\n                last_name: updatedStudent.last_name,\n                email: updatedStudent.email,\n                phone: updatedStudent.phone,\n                date_of_birth: updatedStudent.date_of_birth,\n                gender: updatedStudent.gender,\n                address: updatedStudent.address,\n                blood_group: updatedStudent.blood_group,\n                nationality: updatedStudent.nationality,\n                religion: updatedStudent.religion,\n                category: updatedStudent.category,\n                mother_tongue: updatedStudent.mother_tongue,\n                previous_school: updatedStudent.previous_school,\n                medical_conditions: updatedStudent.medical_conditions,\n                emergency_contact_name: updatedStudent.emergency_contact_name,\n                emergency_contact_phone: updatedStudent.emergency_contact_phone,\n                emergency_contact_relation: updatedStudent.emergency_contact_relation,\n                admission_number: updatedStudent.admission_number,\n                admission_date: updatedStudent.admission_date,\n                roll_number: updatedStudent.roll_number,\n                transport_required: updatedStudent.transport_required,\n                hostel_required: updatedStudent.hostel_required,\n                status: updatedStudent.status,\n                current_class_id: updatedStudent.current_class_id\n            };\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.update(String(updatedStudent.id), updateData);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_15__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_name\",\n            header: \"Emergency Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const contact = row.getValue(\"emergency_contact_name\");\n                return contact || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_phone\",\n            header: \"Emergency Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const contactPhone = row.getValue(\"emergency_contact_phone\");\n                return contactPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 615,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_12__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_13__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                            student: student,\n                            mode: \"edit\",\n                            onUpdate: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 663,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 679,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 676,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 675,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 690,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 699,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 698,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_11__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 706,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 705,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 702,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 689,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 687,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 718,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 720,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 715,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 714,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 730,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 737,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 728,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 713,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 686,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_14__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});