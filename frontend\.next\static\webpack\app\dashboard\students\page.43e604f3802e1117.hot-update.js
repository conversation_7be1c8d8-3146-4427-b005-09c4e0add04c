"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.update(updatedStudent.id, updatedStudent);\n            setStudents((prev)=>prev.map((student)=>student.id === updatedStudent.id ? updatedStudent : student));\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        toast({\n            title: \"Import Feature\",\n            description: \"Student import functionality will be implemented with file upload.\"\n        });\n    };\n    const handlePrintAll = ()=>{\n        window.print();\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 294,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 290,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_name\",\n            header: \"Parent/Guardian\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardian = row.getValue(\"guardian_name\");\n                return guardian || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_phone\",\n            header: \"Guardian Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardianPhone = row.getValue(\"guardian_phone\");\n                return guardianPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 373,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 400,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__.EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 409,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 405,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 420,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 419,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 414,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 381,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 434,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 433,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 432,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 445,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 448,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 447,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 451,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 456,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 463,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 462,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 459,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 446,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 444,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 475,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 473,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 477,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 472,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 490,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 488,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 487,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 494,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 485,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 470,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 443,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});