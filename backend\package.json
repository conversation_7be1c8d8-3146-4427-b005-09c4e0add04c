{"name": "sms-backend", "version": "1.0.0", "description": "School Management System Backend API", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node src/scripts/runMigrations.js migrate", "migrate:rollback": "node src/scripts/runMigrations.js rollback", "db:setup": "node src/scripts/setupDatabase.js", "db:create": "node src/scripts/setupDatabase.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["school", "management", "system", "api", "nodejs", "mysql"], "author": "", "license": "ISC", "dependencies": {"bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "multer": "^2.0.2", "mysql2": "^3.14.2", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"nodemon": "^3.1.10"}}