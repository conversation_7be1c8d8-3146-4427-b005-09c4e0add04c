"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.update(updatedStudent.id, updatedStudent);\n            setStudents((prev)=>prev.map((student)=>student.id === updatedStudent.id ? updatedStudent : student));\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 501,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_name\",\n            header: \"Emergency Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const contact = row.getValue(\"emergency_contact_name\");\n                return contact || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_phone\",\n            header: \"Emergency Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const contactPhone = row.getValue(\"emergency_contact_phone\");\n                return contactPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__.EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 644,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 643,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 655,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 654,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});