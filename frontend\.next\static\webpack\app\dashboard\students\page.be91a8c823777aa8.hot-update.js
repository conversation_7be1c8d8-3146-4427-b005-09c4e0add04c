"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            // Prepare data for API - only send fields that can be updated\n            const updateData = {\n                first_name: updatedStudent.first_name,\n                last_name: updatedStudent.last_name,\n                email: updatedStudent.email,\n                phone: updatedStudent.phone,\n                date_of_birth: updatedStudent.date_of_birth,\n                gender: updatedStudent.gender,\n                address: updatedStudent.address,\n                blood_group: updatedStudent.blood_group,\n                nationality: updatedStudent.nationality,\n                religion: updatedStudent.religion,\n                category: updatedStudent.category,\n                mother_tongue: updatedStudent.mother_tongue,\n                previous_school: updatedStudent.previous_school,\n                medical_conditions: updatedStudent.medical_conditions,\n                emergency_contact_name: updatedStudent.emergency_contact_name,\n                emergency_contact_phone: updatedStudent.emergency_contact_phone,\n                emergency_contact_relation: updatedStudent.emergency_contact_relation,\n                admission_number: updatedStudent.admission_number,\n                admission_date: updatedStudent.admission_date,\n                roll_number: updatedStudent.roll_number,\n                transport_required: updatedStudent.transport_required,\n                hostel_required: updatedStudent.hostel_required,\n                status: updatedStudent.status,\n                current_class_id: updatedStudent.current_class_id\n            };\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.update(String(updatedStudent.id), updateData);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 506,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 513,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 532,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_name\",\n            header: \"Emergency Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const contact = row.getValue(\"emergency_contact_name\");\n                return contact || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"emergency_contact_phone\",\n            header: \"Emergency Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const contactPhone = row.getValue(\"emergency_contact_phone\");\n                return contactPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 615,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 624,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 628,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 643,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 642,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__.EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 652,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 651,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 647,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 662,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 656,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 623,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 676,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 678,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 675,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 674,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 687,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 694,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 693,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 698,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 697,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 705,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 701,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 688,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 686,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 717,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 715,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 719,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 714,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 713,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 731,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 730,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 729,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 736,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 727,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 712,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 685,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});