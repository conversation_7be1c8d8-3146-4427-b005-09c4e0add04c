# Security & Database Design Specifications

## 🔒 Security Architecture

### Authentication Framework

#### JWT Token Strategy
```javascript
// Token structure and configuration
const tokenConfig = {
  accessToken: {
    expiresIn: '15m',
    algorithm: 'HS256',
    issuer: 'school-management-system',
    audience: 'sms-users'
  },
  refreshToken: {
    expiresIn: '7d',
    algorithm: 'HS256',
    issuer: 'school-management-system',
    audience: 'sms-users'
  }
};

// Token payload structure
const tokenPayload = {
  userId: 'user_uuid',
  email: '<EMAIL>',
  userType: 'admin|teacher|student|parent',
  roles: ['role1', 'role2'],
  permissions: ['permission1', 'permission2'],
  iat: 'issued_at_timestamp',
  exp: 'expiration_timestamp'
};
```

#### Password Security
- **Hashing Algorithm**: bcryptjs with 12 salt rounds minimum
- **Password Requirements**: 
  - Minimum 8 characters
  - At least one uppercase letter
  - At least one lowercase letter
  - At least one number
  - At least one special character
- **Password History**: Prevent reuse of last 5 passwords
- **Account Lockout**: Lock account after 5 failed attempts for 30 minutes

#### Role-Based Access Control (RBAC)
```javascript
// Role hierarchy and permissions
const rolePermissions = {
  admin: [
    'user:create', 'user:read', 'user:update', 'user:delete',
    'student:*', 'teacher:*', 'class:*', 'subject:*',
    'attendance:*', 'result:*', 'fee:*', 'system:*'
  ],
  teacher: [
    'student:read', 'class:read', 'subject:read',
    'attendance:create', 'attendance:read', 'attendance:update',
    'result:create', 'result:read', 'result:update',
    'lesson:*', 'assessment:*'
  ],
  student: [
    'profile:read', 'profile:update',
    'attendance:read:own', 'result:read:own',
    'fee:read:own', 'timetable:read:own'
  ],
  parent: [
    'profile:read', 'profile:update',
    'child:read', 'child:attendance:read',
    'child:result:read', 'child:fee:read'
  ]
};
```

### Input Validation & Sanitization

#### Validation Middleware
```javascript
// Express-validator schemas
const validationSchemas = {
  user: {
    email: {
      isEmail: true,
      normalizeEmail: true,
      errorMessage: 'Valid email required'
    },
    password: {
      isLength: { min: 8, max: 128 },
      matches: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])/,
      errorMessage: 'Password must meet complexity requirements'
    },
    firstName: {
      isLength: { min: 2, max: 50 },
      matches: /^[a-zA-Z\s]+$/,
      trim: true,
      errorMessage: 'First name must be 2-50 characters, letters only'
    }
  }
};
```

#### SQL Injection Prevention
- **Parameterized Queries**: All database queries use prepared statements
- **Input Sanitization**: HTML entities encoding for all user inputs
- **Query Whitelisting**: Predefined query patterns only
- **Database User Permissions**: Minimal required permissions for database user

### API Security Measures

#### Rate Limiting Configuration
```javascript
const rateLimitConfig = {
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    message: 'Too many authentication attempts'
  },
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per window
    message: 'Too many API requests'
  },
  upload: {
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 20, // 20 uploads per hour
    message: 'Too many file uploads'
  }
};
```

#### CORS Configuration
```javascript
const corsOptions = {
  origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000'],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true,
  maxAge: 86400 // 24 hours
};
```

## 🗄️ Database Design

### Core Schema Structure

#### Users Table (Central Authentication)
```sql
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  uuid VARCHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  email VARCHAR(255) NOT NULL UNIQUE,
  password_hash VARCHAR(255) NOT NULL,
  user_type ENUM('admin', 'teacher', 'student', 'parent') NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  date_of_birth DATE,
  gender ENUM('male', 'female', 'other'),
  address TEXT,
  profile_picture VARCHAR(255),
  status ENUM('active', 'inactive', 'suspended', 'graduated') DEFAULT 'active',
  email_verified BOOLEAN DEFAULT FALSE,
  last_login TIMESTAMP NULL,
  password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  failed_login_attempts INT DEFAULT 0,
  locked_until TIMESTAMP NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_email (email),
  INDEX idx_user_type (user_type),
  INDEX idx_status (status),
  INDEX idx_uuid (uuid),
  INDEX idx_name (first_name, last_name),
  INDEX idx_phone (phone)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Academic Structure Tables
```sql
-- Classes/Grades
CREATE TABLE classes (
  id INT AUTO_INCREMENT PRIMARY KEY,
  uuid VARCHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  name VARCHAR(100) NOT NULL,
  grade_level INT NOT NULL,
  section VARCHAR(10),
  academic_year VARCHAR(9) NOT NULL, -- e.g., "2024-2025"
  class_teacher_id INT,
  capacity INT DEFAULT 30,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (class_teacher_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_grade_level (grade_level),
  INDEX idx_academic_year (academic_year),
  INDEX idx_status (status),
  UNIQUE KEY unique_class_section_year (name, section, academic_year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Subjects
CREATE TABLE subjects (
  id INT AUTO_INCREMENT PRIMARY KEY,
  uuid VARCHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  name VARCHAR(100) NOT NULL,
  code VARCHAR(20) NOT NULL UNIQUE,
  description TEXT,
  credit_hours INT DEFAULT 1,
  department VARCHAR(100),
  is_core BOOLEAN DEFAULT TRUE,
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_code (code),
  INDEX idx_department (department),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Student-Specific Tables
```sql
-- Student profiles
CREATE TABLE students (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT NOT NULL UNIQUE,
  student_id VARCHAR(20) NOT NULL UNIQUE,
  admission_number VARCHAR(50) UNIQUE,
  class_id INT,
  admission_date DATE NOT NULL,
  guardian_name VARCHAR(200),
  guardian_phone VARCHAR(20),
  guardian_email VARCHAR(255),
  emergency_contact_name VARCHAR(200),
  emergency_contact_phone VARCHAR(20),
  blood_group VARCHAR(5),
  allergies TEXT,
  medical_conditions TEXT,
  previous_school VARCHAR(200),
  transport_required BOOLEAN DEFAULT FALSE,
  hostel_required BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE SET NULL,
  INDEX idx_student_id (student_id),
  INDEX idx_admission_number (admission_number),
  INDEX idx_class_id (class_id),
  INDEX idx_admission_date (admission_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Parent-Student relationships
CREATE TABLE parent_student_relationships (
  id INT AUTO_INCREMENT PRIMARY KEY,
  parent_id INT NOT NULL,
  student_id INT NOT NULL,
  relationship_type ENUM('father', 'mother', 'guardian', 'other') NOT NULL,
  is_primary_contact BOOLEAN DEFAULT FALSE,
  can_pickup BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (parent_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  UNIQUE KEY unique_parent_student (parent_id, student_id),
  INDEX idx_parent_id (parent_id),
  INDEX idx_student_id (student_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### Attendance System
```sql
CREATE TABLE attendance (
  id INT AUTO_INCREMENT PRIMARY KEY,
  uuid VARCHAR(36) NOT NULL UNIQUE DEFAULT (UUID()),
  student_id INT NOT NULL,
  class_id INT NOT NULL,
  subject_id INT,
  teacher_id INT NOT NULL,
  attendance_date DATE NOT NULL,
  period_number INT,
  status ENUM('present', 'absent', 'late', 'excused') NOT NULL,
  check_in_time TIME,
  check_out_time TIME,
  remarks TEXT,
  marked_by INT NOT NULL,
  marked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
  FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
  FOREIGN KEY (subject_id) REFERENCES subjects(id) ON DELETE SET NULL,
  FOREIGN KEY (teacher_id) REFERENCES users(id) ON DELETE CASCADE,
  FOREIGN KEY (marked_by) REFERENCES users(id) ON DELETE CASCADE,
  
  UNIQUE KEY unique_attendance (student_id, attendance_date, period_number),
  INDEX idx_student_date (student_id, attendance_date),
  INDEX idx_class_date (class_id, attendance_date),
  INDEX idx_teacher_date (teacher_id, attendance_date),
  INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### Data Integrity Constraints

#### Foreign Key Relationships
- **Cascading Deletes**: User deletion cascades to related records
- **Referential Integrity**: All foreign keys properly constrained
- **Orphan Prevention**: Prevent orphaned records through constraints

#### Data Validation Constraints
```sql
-- Add check constraints for data validation
ALTER TABLE users 
ADD CONSTRAINT chk_email_format 
CHECK (email REGEXP '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$');

ALTER TABLE students 
ADD CONSTRAINT chk_admission_date 
CHECK (admission_date <= CURDATE());

ALTER TABLE attendance 
ADD CONSTRAINT chk_attendance_date 
CHECK (attendance_date <= CURDATE());
```

### Performance Optimization

#### Indexing Strategy
```sql
-- Composite indexes for common queries
CREATE INDEX idx_student_class_date ON attendance (student_id, class_id, attendance_date);
CREATE INDEX idx_user_type_status ON users (user_type, status);
CREATE INDEX idx_class_academic_year ON classes (academic_year, status);

-- Full-text search indexes
CREATE FULLTEXT INDEX idx_user_search ON users (first_name, last_name, email);
CREATE FULLTEXT INDEX idx_subject_search ON subjects (name, code, description);
```

#### Query Optimization Guidelines
- **Limit Result Sets**: Always use LIMIT for large datasets
- **Avoid SELECT ***: Specify required columns only
- **Use Prepared Statements**: Prevent SQL injection and improve performance
- **Optimize JOINs**: Use appropriate JOIN types and conditions
- **Partition Large Tables**: Consider partitioning for very large datasets

### Backup and Recovery Strategy

#### Automated Backup System
```bash
# Daily backup script
#!/bin/bash
BACKUP_DIR="/var/backups/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="school_management_system"

# Create encrypted backup
mysqldump --single-transaction --routines --triggers \
  --user=$DB_USER --password=$DB_PASS \
  $DB_NAME | gzip | gpg --cipher-algo AES256 --compress-algo 1 \
  --symmetric --output $BACKUP_DIR/backup_$DATE.sql.gz.gpg

# Retain backups for 30 days
find $BACKUP_DIR -name "backup_*.sql.gz.gpg" -mtime +30 -delete
```

#### Recovery Procedures
- **Point-in-Time Recovery**: Binary log-based recovery
- **Full Database Restore**: Complete database restoration from backup
- **Selective Recovery**: Table-level or data-specific recovery
- **Disaster Recovery**: Cross-site backup and recovery procedures

This comprehensive security and database design ensures robust data protection, optimal performance, and reliable system operation while maintaining strict security standards throughout the application.
