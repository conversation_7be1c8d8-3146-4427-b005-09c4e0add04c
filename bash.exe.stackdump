Stack trace:
Frame         Function      Args
0007FFFFBE20  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFBE20, 0007FFFFAD20) msys-2.0.dll+0x1FE8E
0007FFFFBE20  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFC0F8) msys-2.0.dll+0x67F9
0007FFFFBE20  000210046832 (000210286019, 0007FFFFBCD8, 0007FFFFBE20, 000000000000) msys-2.0.dll+0x6832
0007FFFFBE20  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFBE20  000210068E24 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFC100  00021006A225 (0007FFFFBE30, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF837680000 ntdll.dll
7FF8358E0000 KERNEL32.DLL
7FF834E10000 KERNELBASE.dll
7FF8356C0000 USER32.dll
7FF834890000 win32u.dll
7FF837280000 GDI32.dll
7FF8348C0000 gdi32full.dll
7FF834D60000 msvcp_win.dll
7FF834A90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8362C0000 advapi32.dll
7FF836500000 msvcrt.dll
7FF8370E0000 sechost.dll
7FF836120000 RPCRT4.dll
7FF833CE0000 CRYPTBASE.DLL
7FF835380000 bcryptPrimitives.dll
7FF837030000 IMM32.DLL
