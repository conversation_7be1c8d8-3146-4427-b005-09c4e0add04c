"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.update(updatedStudent.id, updatedStudent);\n            setStudents((prev)=>prev.map((student)=>student.id === updatedStudent.id ? updatedStudent : student));\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        window.print();\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 356,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 375,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_name\",\n            header: \"Parent/Guardian\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardian = row.getValue(\"guardian_name\");\n                return guardian || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_phone\",\n            header: \"Guardian Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardianPhone = row.getValue(\"guardian_phone\");\n                return guardianPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 458,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 468,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 471,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 486,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 485,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__.EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 495,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 494,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 504,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 499,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 520,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 521,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 518,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 517,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 530,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 533,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 532,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 544,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 531,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 529,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 559,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 560,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 557,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 556,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 575,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 579,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 570,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 555,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 528,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});