"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.update(updatedStudent.id, updatedStudent);\n            setStudents((prev)=>prev.map((student)=>student.id === updatedStudent.id ? updatedStudent : student));\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(id)));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(student.id)));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id,\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender,\n                    'Date of Birth': student.date_of_birth,\n                    'Guardian': student.guardian_name,\n                    'Guardian Phone': student.guardian_phone,\n                    'Class': student.class_name || '',\n                    'Status': student.status,\n                    'Admission Date': student.admission_date\n                }));\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        toast({\n            title: \"Import Feature\",\n            description: \"Student import functionality will be implemented with file upload.\"\n        });\n    };\n    const handlePrintAll = ()=>{\n        window.print();\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 251,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 274,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_name\",\n            header: \"Parent/Guardian\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardian = row.getValue(\"guardian_name\");\n                return guardian || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_phone\",\n            header: \"Guardian Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardianPhone = row.getValue(\"guardian_phone\");\n                return guardianPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 353,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 376,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__.EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 385,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 400,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 399,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 413,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 412,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 428,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 427,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 443,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 442,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 439,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 426,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 424,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 455,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 453,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 457,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 452,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 451,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 467,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 474,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 450,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 423,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});