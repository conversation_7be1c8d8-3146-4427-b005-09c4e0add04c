{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: NO)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-22 14:21:53"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: NO)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-22 14:22:35"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: NO)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: NO)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: NO)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-23 14:26:46"}
{"code":"ER_BAD_DB_ERROR","errno":1049,"level":"error","message":"Database connection failed: Unknown database 'school_management_system'","service":"sms-backend","sqlMessage":"Unknown database 'school_management_system'","sqlState":"42000","stack":"Error: Unknown database 'school_management_system'\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-23 14:28:31"}
{"code":"ER_BAD_DB_ERROR","errno":1049,"level":"error","message":"Database connection failed: Unknown database 'school_management_system'","service":"sms-backend","sqlMessage":"Unknown database 'school_management_system'","sqlState":"42000","stack":"Error: Unknown database 'school_management_system'\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-23 14:28:35"}
{"error":"Expression of generated column 'percentage' contains a disallowed function.","level":"error","message":"Database query error:","params":[],"query":"\n    CREATE TABLE IF NOT EXISTS assessment_results (\n      id INT AUTO_INCREMENT PRIMARY KEY,\n      assessment_id INT NOT NULL,\n      student_id INT NOT NULL,\n      marks_obtained DECIMAL(5,2) NOT NULL,\n      percentage DECIMAL(5,2) GENERATED ALWAYS AS ((marks_obtained / (SELECT total_marks FROM assessments WHERE id = assessment_id)) * 100) STORED,\n      grade VARCHAR(5),\n      remarks TEXT,\n      is_absent BOOLEAN DEFAULT FALSE,\n      submission_date TIMESTAMP,\n      graded_by INT,\n      graded_at TIMESTAMP,\n      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n      \n      FOREIGN KEY (assessment_id) REFERENCES assessments(id) ON DELETE CASCADE,\n      FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,\n      FOREIGN KEY (graded_by) REFERENCES users(id) ON DELETE SET NULL,\n      INDEX idx_assessment_id (assessment_id),\n      INDEX idx_student_id (student_id),\n      INDEX idx_marks_obtained (marks_obtained),\n      INDEX idx_percentage (percentage),\n      INDEX idx_grade (grade),\n      INDEX idx_is_absent (is_absent),\n      UNIQUE KEY unique_assessment_student (assessment_id, student_id)\n    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;\n  ","service":"sms-backend","timestamp":"2025-07-23 14:57:02"}
{"level":"error","message":"Migration failed: 006_create_transportation_and_communication_tables.js Unexpected identifier 'recipients'","service":"sms-backend","stack":"C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\migrations\\006_create_transportation_and_communication_tables.js:215\n  -- Message recipients table\n             ^^^^^^^^^^\n\nSyntaxError: Unexpected identifier 'recipients'\n    at wrapSafe (node:internal/modules/cjs/loader:1486:18)\n    at Module._compile (node:internal/modules/cjs/loader:1528:20)\n    at Object..js (node:internal/modules/cjs/loader:1706:10)\n    at Module.load (node:internal/modules/cjs/loader:1289:32)\n    at Function._load (node:internal/modules/cjs/loader:1108:12)\n    at TracingChannel.traceSync (node:diagnostics_channel:322:14)\n    at wrapModuleLoad (node:internal/modules/cjs/loader:220:24)\n    at Module.require (node:internal/modules/cjs/loader:1311:12)\n    at require (node:internal/modules/helpers:136:16)\n    at MigrationRunner.executeMigration (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\migrations\\migrationRunner.js:50:25)","timestamp":"2025-07-23 15:03:48"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:16:29"}
{"ip":"::ffff:127.0.0.1","level":"error","message":"Error: Expected ',' or '}' after property value in JSON at position 130 (line 7 column 3)","method":"POST","service":"sms-backend","stack":"SyntaxError: Expected ',' or '}' after property value in JSON at position 130 (line 7 column 3)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:211:14)\n    at invokeCallback (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:518:28)\n    at endReadableNT (node:internal/streams/readable:1698:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","timestamp":"2025-07-23 15:29:12","url":"/api/auth","userAgent":"Thunder Client (https://www.thunderclient.com)"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:30:37"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Transaction error: Field 'uuid' doesn't have a default value","service":"sms-backend","sql":"INSERT INTO users (email, password_hash, user_type, status) VALUES (?, ?, 'admin', 'active')","sqlMessage":"Field 'uuid' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'uuid' doesn't have a default value\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:215:5)","timestamp":"2025-07-23 15:32:16"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Create admin error: Field 'uuid' doesn't have a default value","service":"sms-backend","sql":"INSERT INTO users (email, password_hash, user_type, status) VALUES (?, ?, 'admin', 'active')","sqlMessage":"Field 'uuid' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'uuid' doesn't have a default value\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:215:5)","timestamp":"2025-07-23 15:32:16"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:44:08"}
{"code":"ER_BAD_NULL_ERROR","errno":1048,"level":"error","message":"Transaction error: Column 'role_id' cannot be null","service":"sms-backend","sql":"INSERT INTO user_roles (user_id, role_id) VALUES (LAST_INSERT_ID(), (SELECT id FROM roles WHERE name = ? LIMIT 1))","sqlMessage":"Column 'role_id' cannot be null","sqlState":"23000","stack":"Error: Column 'role_id' cannot be null\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:244:5)","timestamp":"2025-07-23 15:44:24"}
{"code":"ER_BAD_NULL_ERROR","errno":1048,"level":"error","message":"Create admin error: Column 'role_id' cannot be null","service":"sms-backend","sql":"INSERT INTO user_roles (user_id, role_id) VALUES (LAST_INSERT_ID(), (SELECT id FROM roles WHERE name = ? LIMIT 1))","sqlMessage":"Column 'role_id' cannot be null","sqlState":"23000","stack":"Error: Column 'role_id' cannot be null\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createAdmin (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\adminController.js:244:5)","timestamp":"2025-07-23 15:44:24"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-23 15:48:23"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:48:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Login error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at login (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:87:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:48:42"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:48:46"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Login error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at login (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:87:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:48:46"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:49:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Login error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at login (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:87:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:49:31"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 15:55:33"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 15:55:33"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 15:56:21"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 15:56:45"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 15:56:45"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:03:53"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 16:04:17"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 16:04:17"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n      SELECT\n        COUNT(*) as total_records,\n        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n      FROM attendance\n      WHERE date = ?\n    ","service":"sms-backend","timestamp":"2025-07-23 16:23:39"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 16:44:35"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 16:44:35"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 16:44:46"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 17:03:05"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 17:03:05"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-23"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-23 17:03:18"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-23 17:03:22"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:255:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-23 17:03:22"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:06:27"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:09:41"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:11:27"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:11:58"}
{"code":"ER_BAD_DB_ERROR","errno":1049,"level":"error","message":"Database connection failed: Unknown database 'school_management_system'","service":"sms-backend","sqlMessage":"Unknown database 'school_management_system'","sqlState":"42000","stack":"Error: Unknown database 'school_management_system'\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 02:14:12"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 02:45:16"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get admins error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getAdmins (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\adminController.js:113:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:45:16"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 02:45:31"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get admins error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        u.id,\n        u.uuid,\n        u.first_name,\n        u.last_name,\n        u.email,\n        u.phone,\n        u.status,\n        u.last_login,\n        u.created_at,\n        GROUP_CONCAT(r.name) as roles,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM users u\n      LEFT JOIN user_roles ur ON u.id = ur.user_id\n      LEFT JOIN roles r ON ur.role_id = r.id\n      WHERE u.user_type = 'admin'\n      GROUP BY u.id\n      ORDER BY u.created_at DESC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getAdmins (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\adminController.js:113:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:45:31"}
{"level":"error","message":"Error generating access token: secretOrPrivateKey must have a value","service":"sms-backend","stack":"Error: secretOrPrivateKey must have a value\n    at module.exports [as sign] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:11:16)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:48:53"}
{"level":"error","message":"Login error: Token generation failed","service":"sms-backend","stack":"Error: Token generation failed\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:22:11)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:48:53"}
{"level":"error","message":"Error generating access token: secretOrPrivateKey must have a value","service":"sms-backend","stack":"Error: secretOrPrivateKey must have a value\n    at module.exports [as sign] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:11:16)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:49:45"}
{"level":"error","message":"Login error: Token generation failed","service":"sms-backend","stack":"Error: Token generation failed\n    at generateAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:22:11)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:108:18)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:49:45"}
{"level":"error","message":"Error generating refresh token: secretOrPrivateKey must have a value","service":"sms-backend","stack":"Error: secretOrPrivateKey must have a value\n    at module.exports [as sign] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\jsonwebtoken\\sign.js:111:20)\n    at generateRefreshToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:33:16)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:109:19)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:51:30"}
{"level":"error","message":"Login error: Refresh token generation failed","service":"sms-backend","stack":"Error: Refresh token generation failed\n    at generateRefreshToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:44:11)\n    at generateTokenPair (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:109:19)\n    at login (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:57:20)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 02:51:30"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:01"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:01"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:12"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 02:57:12"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 03:02:10"}
{"error":"Cannot read properties of undefined (reading 'execute')","level":"error","message":"Database query error:","params":["<EMAIL>"],"query":"SELECT id FROM users WHERE email = ?","service":"sms-backend","timestamp":"2025-07-26 03:03:26"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:19:20"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:20:00"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:21:11"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:11"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:11"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:44"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:44"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:48"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:21:48"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:23:30"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:23:30"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 03:31:24"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 03:33:11"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 03:33:42"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:41:54"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:41:55"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:42:15"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:15"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:15"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-26 03:42:35"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:35"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-26 03:42:35"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 03:46:13"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:47:42"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:47:42"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:27"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:50:56"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:50:56"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:50:57"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:51:09"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:51:09"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 03:51:15"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:51:31"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:51:31"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:52:16"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:52:16"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:54:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:54:14"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:54:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:54:15"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:54:15"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:54:15"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:56:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:56:23"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:56:29"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:29"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:29"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:29"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:56:48"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:48"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:56:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:56:48"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:57:03"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:57:03"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:04"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:04"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:04"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:04"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 03:57:21"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:21"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.current_class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1076:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 03:57:21"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 04:02:07"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:519:28)\n    at addChunk (node:internal/streams/readable:559:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:510:3)\n    at Readable.push (node:internal/streams/readable:390:5)","timestamp":"2025-07-26 04:02:08"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:03:10"}
{"error":"Unknown column 'grade_level_id' in 'field list'","level":"error","message":"Database query error:","params":["Class 1A",1,"Grade 1 Section A"],"query":"INSERT IGNORE INTO classes (name, grade_level_id, description) VALUES (?, ?, ?)","service":"sms-backend","timestamp":"2025-07-26 04:03:18"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:03:54"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:04:43"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:04:58"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:05:11"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:05:31"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:06:03"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:06:16"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:07:21"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:07:34"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:07:50"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:09:45"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:09:58"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:10:12"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:10:38"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:10:53"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:11:38"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:11:38"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:11:38"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:11:38"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:24:59"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:24:59"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:24:59"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:24:59"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:25:35"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:25:35"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:25:35"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.address,\n        s.admission_date,\n        s.passport_photo,\n        s.status,\n        s.created_at,\n        u.email,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY s.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1110:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:25:35"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:28:49"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:17"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:17"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:17"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:17"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:23"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:23"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:23"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:23"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:30:30"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get students error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.date_of_birth,\n        u.gender,\n        u.phone,\n        u.address,\n        s.admission_date,\n        s.admission_number,\n        s.roll_number,\n        s.blood_group,\n        s.nationality,\n        s.religion,\n        s.medical_conditions,\n        s.emergency_contact_name,\n        s.emergency_contact_phone,\n        s.status,\n        s.created_at,\n        u.email,\n        u.profile_picture as passport_photo,\n        c.name as class_name,\n        gl.name as grade_level,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as full_name\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\studentController.js:1127:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:30:30"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:31:10"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.email\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:31:17"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:31:34"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[0,10],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        u.first_name,\n        u.last_name,\n        u.email\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      WHERE 1=1\n      ORDER BY u.first_name ASC\n      LIMIT ?, ?\n    ","service":"sms-backend","timestamp":"2025-07-26 04:31:46"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:31:59"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:32:10"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:33:57"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:35:11"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:38:18"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:38:18"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:38:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:38:26"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:49:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:49:51"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:50:53"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:50:53"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:51:17"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:51:17"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:51:49"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:51:49"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:53:30"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:53:30"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:53:39"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:53:39"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:54:26"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:54:26"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 04:54:26"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 04:54:26"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:136:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:56:08"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:137:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 04:57:28"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 04:57:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 04:57:48"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:07:40"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:07:40"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:12:48"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:12:48"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:12:49"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:16:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:16:47"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:16:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:16:47"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:21:51"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:21:51"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:21:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:21:51"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:22:06"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:22:06"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:25:39"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:25:39"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:25:39"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:25:39"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:37:23"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:37:23"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:37:23"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:37:23"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:38:20"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:38:20"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-26 05:38:20"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-26 05:38:20"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:54:55"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:54:55"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1898:16)\n    at listenInCluster (node:net:1946:12)\n    at Server.listen (node:net:2044:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\server.js:137:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","syscall":"listen","timestamp":"2025-07-26 05:55:37"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:37"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\orange\\sms\\sms-backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-26"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-26 05:57:42"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:25"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:25"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:25"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:25"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:45:30"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:45:30"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:31"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:31"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:31"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 14:45:31"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:47:03"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:47:03"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:47:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:47:51"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 14:47:52"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 14:47:52"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 14:47:52"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 14:47:52"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:47:55"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1086:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:47:55"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:47:55"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get students error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM students s\n      LEFT JOIN users u ON s.user_id = u.id\n      LEFT JOIN classes c ON s.class_id = c.id\n      LEFT JOIN grade_levels gl ON c.grade_level = gl.name\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudents (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1086:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:47:55"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:47:58"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:47:58"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:48:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:48:14"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:48:14"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:48:14"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:50:25"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:50:25"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:50:25"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:50:25"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:50:25"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:50:25"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM subjects s\n      LEFT JOIN departments d ON s.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:52:47"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get subjects error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM subjects s\n      LEFT JOIN departments d ON s.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getSubjects (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\subjectController.js:63:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:52:47"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM subjects s\n      LEFT JOIN departments d ON s.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:52:47"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get subjects error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM subjects s\n      LEFT JOIN departments d ON s.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getSubjects (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\subjectController.js:63:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:52:47"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:54:58"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:54:58"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:54:58"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:54:58"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:55:02"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:55:02"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:55:02"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:55:02"}
{"error":"Table 'school_management_system.grade_levels' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:55:02"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.grade_levels' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.grade_levels' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.grade_levels' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:55:02"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:56:00"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:56:00"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:56:00"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:56:00"}
{"error":"Unknown column 'c.grade_level_id' in 'on clause'","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 14:56:00"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get classes error: Unknown column 'c.grade_level_id' in 'on clause'","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Unknown column 'c.grade_level_id' in 'on clause'","sqlState":"42S22","stack":"Error: Unknown column 'c.grade_level_id' in 'on clause'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:65:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 14:56:00"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\server.js:137:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-07-28 14:56:50"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\server.js:137:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-07-28 14:57:28"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:57:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:57:37"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 14:57:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:57:37"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 14:57:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:57:37"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 14:58:51"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:58:51"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 14:59:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:59:42"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 14:59:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:59:42"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 14:59:42"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 14:59:42"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 15:00:01"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:00:01"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:00:02"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:00:02"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:00:02"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:00:02"}
{"error":"Table 'school_management_system.admins' doesn't exist","level":"error","message":"Database query error:","params":[2],"query":"SELECT * FROM admins WHERE user_id = ?","service":"sms-backend","timestamp":"2025-07-28 15:00:33"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get profile error: Table 'school_management_system.admins' doesn't exist","service":"sms-backend","sql":"SELECT * FROM admins WHERE user_id = ?","sqlMessage":"Table 'school_management_system.admins' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.admins' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getProfile (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\authController.js:256:30)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:00:33"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:00:33"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:00:33"}
{"error":"Table 'school_management_system.teacher_subjects' doesn't exist","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:00:33"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.teacher_subjects' doesn't exist","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Table 'school_management_system.teacher_subjects' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.teacher_subjects' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:92:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:00:33"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:04:48"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get classes error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:93:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:04:48"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:04:57"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get classes error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:93:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:04:57"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:04:57"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get classes error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:93:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:04:57"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:05:34"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get classes error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:93:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:05:34"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:09:19"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get classes error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:96:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:09:19"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[10,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:09:19"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get classes error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:96:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:09:19"}
{"error":"Incorrect arguments to mysqld_stmt_execute","level":"error","message":"Database query error:","params":[20,0],"query":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","service":"sms-backend","timestamp":"2025-07-28 15:10:02"}
{"code":"ER_WRONG_ARGUMENTS","errno":1210,"level":"error","message":"Get classes error: Incorrect arguments to mysqld_stmt_execute","service":"sms-backend","sql":"\n      SELECT \n        c.id,\n        c.name,\n        c.capacity,\n        c.room_number,\n        c.status,\n        c.created_at,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(u.first_name, ' ', u.last_name) as class_teacher_name,\n        (SELECT COUNT(*) FROM students s WHERE s.current_class_id = c.id AND s.status = 'active') as student_count,\n        (SELECT COUNT(DISTINCT ts.subject_id) FROM teacher_subjects ts WHERE ts.class_id = c.id) as subject_count\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      LEFT JOIN users u ON t.user_id = u.id\n      WHERE 1=1\n      ORDER BY gl.level_number, c.name ASC\n      LIMIT ? OFFSET ?\n    ","sqlMessage":"Incorrect arguments to mysqld_stmt_execute","sqlState":"HY000","stack":"Error: Incorrect arguments to mysqld_stmt_execute\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:96:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:10:02"}
{"error":"Table 'school_management_system.academic_years' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 15:16:21"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get classes error: Table 'school_management_system.academic_years' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.academic_years' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.academic_years' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClasses (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:68:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 15:16:21"}
{"error":"Field 'uuid' doesn't have a default value","level":"error","message":"Database query error:","params":["Test Class A","868ea688-6bba-11f0-a56c-040e3c476597",1,null,30,"Room 101"],"query":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","service":"sms-backend","timestamp":"2025-07-28 15:17:49"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Create class error: Field 'uuid' doesn't have a default value","service":"sms-backend","sql":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","sqlMessage":"Field 'uuid' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'uuid' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at createClass (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:260:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:17:49"}
{"error":"Field 'grade_level' doesn't have a default value","level":"error","message":"Database query error:","params":["Test Class A","868ea688-6bba-11f0-a56c-040e3c476597",1,null,30,"Room 101"],"query":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","service":"sms-backend","timestamp":"2025-07-28 15:19:23"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Create class error: Field 'grade_level' doesn't have a default value","service":"sms-backend","sql":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","sqlMessage":"Field 'grade_level' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'grade_level' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at createClass (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:260:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:19:23"}
{"level":"error","message":"Authentication error: Invalid token","service":"sms-backend","stack":"Error: Invalid token\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:63:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at trim_prefix (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:328:13)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:286:9\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","timestamp":"2025-07-28 15:20:05"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\server.js:137:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-07-28 15:22:50"}
{"error":"Field 'grade_level' doesn't have a default value","level":"error","message":"Database query error:","params":["Test Class A","868ea688-6bba-11f0-a56c-040e3c476597","1",null,30,"Room 101"],"query":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","service":"sms-backend","timestamp":"2025-07-28 15:25:32"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Create class error: Field 'grade_level' doesn't have a default value","service":"sms-backend","sql":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","sqlMessage":"Field 'grade_level' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'grade_level' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at createClass (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:260:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:25:32"}
{"error":"Field 'grade_level' doesn't have a default value","level":"error","message":"Database query error:","params":["Test Class B","868ea688-6bba-11f0-a56c-040e3c476597",1,null,25,"Room 102"],"query":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","service":"sms-backend","timestamp":"2025-07-28 15:25:32"}
{"code":"ER_NO_DEFAULT_FOR_FIELD","errno":1364,"level":"error","message":"Create class error: Field 'grade_level' doesn't have a default value","service":"sms-backend","sql":"\n      INSERT INTO classes (name, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, 'active')\n    ","sqlMessage":"Field 'grade_level' doesn't have a default value","sqlState":"HY000","stack":"Error: Field 'grade_level' doesn't have a default value\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at createClass (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:260:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:25:32"}
{"error":"Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)","level":"error","message":"Database query error:","params":["Test Class A","Grade 1","868ea688-6bba-11f0-a56c-040e3c476597","1",null,30,"Room 101"],"query":"\n      INSERT INTO classes (name, grade_level, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, ?, 'active')\n    ","service":"sms-backend","timestamp":"2025-07-28 15:27:10"}
{"code":"ER_NO_REFERENCED_ROW_2","errno":1452,"level":"error","message":"Create class error: Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)","service":"sms-backend","sql":"\n      INSERT INTO classes (name, grade_level, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, ?, 'active')\n    ","sqlMessage":"Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)","sqlState":"23000","stack":"Error: Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at createClass (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:275:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:27:10"}
{"error":"Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)","level":"error","message":"Database query error:","params":["Test Class B","Grade 1","868ea688-6bba-11f0-a56c-040e3c476597",1,null,25,"Room 102"],"query":"\n      INSERT INTO classes (name, grade_level, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, ?, 'active')\n    ","service":"sms-backend","timestamp":"2025-07-28 15:27:10"}
{"code":"ER_NO_REFERENCED_ROW_2","errno":1452,"level":"error","message":"Create class error: Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)","service":"sms-backend","sql":"\n      INSERT INTO classes (name, grade_level, grade_level_id, academic_year_id, class_teacher_id, capacity, room_number, status)\n      VALUES (?, ?, ?, ?, ?, ?, ?, 'active')\n    ","sqlMessage":"Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)","sqlState":"23000","stack":"Error: Cannot add or update a child row: a foreign key constraint fails (`school_management_system`.`classes`, CONSTRAINT `classes_ibfk_1` FOREIGN KEY (`academic_year_id`) REFERENCES `academic_years_new` (`id`) ON DELETE SET NULL)\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at createClass (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:275:26)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 15:27:10"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 15:55:10"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get teachers error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getTeachers (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\teacherController.js:1272:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 15:55:10"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 16:26:36"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get teachers error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getTeachers (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\teacherController.js:1272:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 16:26:36"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 16:28:43"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get teachers error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getTeachers (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\teacherController.js:1272:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 16:28:43"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 16:32:23"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get teachers error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getTeachers (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\teacherController.js:1272:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 16:32:23"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":["8"],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.status,\n        s.admission_date,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        u.email\n      FROM students s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","service":"sms-backend","timestamp":"2025-07-28 16:52:45"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.status,\n        s.admission_date,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        u.email\n      FROM students s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\routes\\classes.js:258:28\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 16:52:45"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":["8"],"query":"\n      SELECT\n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.status,\n        s.admission_date,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        u.email\n      FROM students s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","service":"sms-backend","timestamp":"2025-07-28 16:53:39"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class students error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT\n        s.id,\n        s.student_id,\n        s.first_name,\n        s.last_name,\n        s.middle_name,\n        s.date_of_birth,\n        s.gender,\n        s.phone,\n        s.status,\n        s.admission_date,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        u.email\n      FROM students s\n      JOIN users u ON s.user_id = u.id\n      WHERE s.class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\routes\\classes.js:258:28\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 16:53:39"}
{"error":"Unknown column 't.first_name' in 'field list'","level":"error","message":"Database query error:","params":["8"],"query":"\n      SELECT \n        c.*,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        t.id as class_teacher_id\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE c.id = ?\n    ","service":"sms-backend","timestamp":"2025-07-28 16:55:57"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class by ID error: Unknown column 't.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        c.*,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        t.id as class_teacher_id\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE c.id = ?\n    ","sqlMessage":"Unknown column 't.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 't.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClassById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:150:27)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 16:55:57"}
{"error":"Unknown column 't.first_name' in 'field list'","level":"error","message":"Database query error:","params":["8"],"query":"\n      SELECT \n        c.*,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        t.id as class_teacher_id\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE c.id = ?\n    ","service":"sms-backend","timestamp":"2025-07-28 16:57:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class by ID error: Unknown column 't.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        c.*,\n        gl.name as grade_level,\n        gl.level_number,\n        ay.name as academic_year,\n        CONCAT(t.first_name, ' ', t.last_name) as class_teacher_name,\n        t.id as class_teacher_id\n      FROM classes c\n      LEFT JOIN grade_levels gl ON c.grade_level_id = gl.id\n      LEFT JOIN academic_years ay ON c.academic_year_id = ay.id\n      LEFT JOIN teachers t ON c.class_teacher_id = t.id\n      WHERE c.id = ?\n    ","sqlMessage":"Unknown column 't.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 't.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClassById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:150:27)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 16:57:47"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":["8"],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","service":"sms-backend","timestamp":"2025-07-28 16:59:23"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class by ID error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClassById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:175:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 16:59:23"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 17:11:35"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get teachers error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getTeachers (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\teacherController.js:1272:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 17:11:35"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 17:11:52"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 17:11:52"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-28"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-28 17:11:52"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-28 17:11:52"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":["9"],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","service":"sms-backend","timestamp":"2025-07-28 17:33:31"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class by ID error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClassById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:175:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 17:33:31"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":["9"],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","service":"sms-backend","timestamp":"2025-07-28 17:40:37"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class by ID error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClassById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:175:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 17:40:37"}
{"error":"Unknown column 's.first_name' in 'field list'","level":"error","message":"Database query error:","params":["9"],"query":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","service":"sms-backend","timestamp":"2025-07-28 17:41:46"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get class by ID error: Unknown column 's.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        s.id,\n        s.student_id,\n        CONCAT(s.first_name, ' ', s.last_name) as full_name,\n        s.status,\n        s.admission_date\n      FROM students s\n      WHERE s.current_class_id = ? AND s.status = 'active'\n      ORDER BY s.first_name, s.last_name\n    ","sqlMessage":"Unknown column 's.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 's.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getClassById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\classController.js:175:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-28 17:41:46"}
{"error":"Table 'school_management_system.departments' doesn't exist","level":"error","message":"Database query error:","params":[],"query":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","service":"sms-backend","timestamp":"2025-07-28 17:50:44"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Get teachers error: Table 'school_management_system.departments' doesn't exist","service":"sms-backend","sql":"\n      SELECT COUNT(*) as total\n      FROM teachers t\n      LEFT JOIN users u ON t.user_id = u.id\n      LEFT JOIN departments d ON t.department_id = d.id\n      WHERE 1=1\n    ","sqlMessage":"Table 'school_management_system.departments' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.departments' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getTeachers (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\teacherController.js:1272:31)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at handleValidationErrors (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\validation.js:21:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)","timestamp":"2025-07-28 17:50:44"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-28 17:53:27"}
{"address":"::","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use :::5000","port":5000,"service":"sms-backend","stack":"Error: listen EADDRINUSE: address already in use :::5000\n    at Server.setupListenHandle [as _listen2] (node:net:1937:16)\n    at listenInCluster (node:net:1994:12)\n    at Server.listen (node:net:2099:7)\n    at Function.listen (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\application.js:635:24)\n    at startServer (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\server.js:141:9)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","syscall":"listen","timestamp":"2025-07-29 11:12:56"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-29"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-29 11:21:06"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-29 11:21:06"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-29"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-29 11:21:06"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-29 11:21:06"}
{"level":"error","message":"Authentication error: Token expired","service":"sms-backend","stack":"Error: Token expired\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:61:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-29 14:57:41"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-29"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-29 14:58:07"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-29 14:58:07"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-29"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-29 14:58:07"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-29 14:58:07"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-29"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-29 20:53:10"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-29 20:53:10"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-29"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-29 20:53:10"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-29 20:53:10"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Transaction error: Unknown column 'first_name' in 'field list'","service":"sms-backend","sql":"INSERT INTO students (\n          user_id, student_id, first_name, last_name, middle_name, date_of_birth, gender,\n          blood_group, nationality, religion, address, phone, emergency_contact_name,\n          emergency_contact_phone, emergency_contact_relationship, admission_date,\n          admission_number, class_id, medical_conditions, allergies\n        ) VALUES (\n          LAST_INSERT_ID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?\n        )","sqlMessage":"Unknown column 'first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'first_name' in 'field list'\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1373:5)","timestamp":"2025-07-29 21:31:00"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Create student error: Unknown column 'first_name' in 'field list'","service":"sms-backend","sql":"INSERT INTO students (\n          user_id, student_id, first_name, last_name, middle_name, date_of_birth, gender,\n          blood_group, nationality, religion, address, phone, emergency_contact_name,\n          emergency_contact_phone, emergency_contact_relationship, admission_date,\n          admission_number, class_id, medical_conditions, allergies\n        ) VALUES (\n          LAST_INSERT_ID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?\n        )","sqlMessage":"Unknown column 'first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'first_name' in 'field list'\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1373:5)","timestamp":"2025-07-29 21:31:00"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Transaction error: Unknown column 'first_name' in 'field list'","service":"sms-backend","sql":"INSERT INTO students (\n          user_id, student_id, first_name, last_name, middle_name, date_of_birth, gender,\n          blood_group, nationality, religion, address, phone, emergency_contact_name,\n          emergency_contact_phone, emergency_contact_relationship, admission_date,\n          admission_number, class_id, medical_conditions, allergies\n        ) VALUES (\n          LAST_INSERT_ID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?\n        )","sqlMessage":"Unknown column 'first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'first_name' in 'field list'\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1391:5)","timestamp":"2025-07-29 21:37:27"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Create student error: Unknown column 'first_name' in 'field list'","service":"sms-backend","sql":"INSERT INTO students (\n          user_id, student_id, first_name, last_name, middle_name, date_of_birth, gender,\n          blood_group, nationality, religion, address, phone, emergency_contact_name,\n          emergency_contact_phone, emergency_contact_relationship, admission_date,\n          admission_number, class_id, medical_conditions, allergies\n        ) VALUES (\n          LAST_INSERT_ID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?\n        )","sqlMessage":"Unknown column 'first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'first_name' in 'field list'\n    at PromisePoolConnection.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\connection.js:47:22)\n    at executeTransaction (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:41)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async createStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1391:5)","timestamp":"2025-07-29 21:37:27"}
{"code":"ER_BAD_FIELD_ERROR","level":"error","message":"Error details: Unknown column 'first_name' in 'field list'","service":"sms-backend","sql":"INSERT INTO students (\n          user_id, student_id, first_name, last_name, middle_name, date_of_birth, gender,\n          blood_group, nationality, religion, address, phone, emergency_contact_name,\n          emergency_contact_phone, emergency_contact_relationship, admission_date,\n          admission_number, class_id, medical_conditions, allergies\n        ) VALUES (\n          LAST_INSERT_ID(), ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?\n        )","sqlMessage":"Unknown column 'first_name' in 'field list'","sqlState":"42S22","timestamp":"2025-07-29 21:37:27"}
{"level":"error","message":"Get student by ID error: res.status is not a function","service":"sms-backend","stack":"TypeError: res.status is not a function\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1167:18)\n    at createStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1393:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 21:53:54"}
{"level":"error","message":"Create student error: res.status is not a function","service":"sms-backend","stack":"TypeError: res.status is not a function\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1241:9)\n    at createStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1393:33)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-29 21:53:54"}
{"level":"error","message":"Error details: res.status is not a function","service":"sms-backend","timestamp":"2025-07-29 21:53:54"}
{"level":"error","message":"Authentication error: Token expired","service":"sms-backend","stack":"Error: Token expired\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:61:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-30 15:03:09"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-30"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-30 15:03:26"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-30 15:03:26"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-30"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-30 15:03:26"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-30 15:03:26"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 14:42:38"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 14:42:38"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 14:42:38"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 14:42:38"}
{"level":"error","message":"Authentication error: Token expired","service":"sms-backend","stack":"Error: Token expired\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:61:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-07-31 15:05:50"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 15:06:08"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 15:06:08"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 15:06:08"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 15:06:08"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["1"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:11:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:11:47"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["1"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:11:47"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:11:47"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["1"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:14:27"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:14:27"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["1"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:14:27"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:14:27"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["3"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:14:41"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:14:41"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["3"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:14:41"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:14:41"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["3"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:14:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:14:51"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["3"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:14:51"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:14:51"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["3"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:18:21"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:18:21"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["3"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:18:21"}
{"code":"ER_BAD_FIELD_ERROR","errno":1054,"level":"error","message":"Get student by ID error: Unknown column 'p.first_name' in 'field list'","service":"sms-backend","sql":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","sqlMessage":"Unknown column 'p.first_name' in 'field list'","sqlState":"42S22","stack":"Error: Unknown column 'p.first_name' in 'field list'\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:48:31)\n    at getStudentById (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1233:27)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-07-31 15:18:21"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-31 15:20:47"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-31 15:21:53"}
{"code":"ER_ACCESS_DENIED_ERROR","errno":1045,"level":"error","message":"Database connection failed: Access denied for user 'root'@'localhost' (using password: YES)","service":"sms-backend","sqlMessage":"Access denied for user 'root'@'localhost' (using password: YES)","sqlState":"28000","stack":"Error: Access denied for user 'root'@'localhost' (using password: YES)\n    at Packet.asError (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packets\\packet.js:740:17)\n    at ClientHandshake.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\commands\\command.js:29:26)\n    at PoolConnection.handlePacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:475:34)\n    at PacketParser.onPacket (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:93:12)\n    at PacketParser.executeStart (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\packet_parser.js:75:16)\n    at Socket.<anonymous> (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\base\\connection.js:100:25)\n    at Socket.emit (node:events:518:28)\n    at addChunk (node:internal/streams/readable:561:12)\n    at readableAddChunkPushByteMode (node:internal/streams/readable:512:3)\n    at Readable.push (node:internal/streams/readable:392:5)","timestamp":"2025-07-31 15:22:42"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":[1],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:23:28"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":["1"],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:23:28"}
{"error":"Unknown column 'p.first_name' in 'field list'","level":"error","message":"Database query error:","params":[1],"query":"\n      SELECT \n        p.id,\n        p.first_name,\n        p.last_name,\n        p.relationship,\n        p.phone,\n        p.work_phone,\n        p.occupation,\n        p.address,\n        sp.is_primary,\n        u.email\n      FROM parents p\n      JOIN student_parents sp ON p.id = sp.parent_id\n      JOIN users u ON p.user_id = u.id\n      WHERE sp.student_id = ?\n      ORDER BY sp.is_primary DESC\n    ","service":"sms-backend","timestamp":"2025-07-31 15:24:25"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 17:30:59"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 17:30:59"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 17:30:59"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 17:30:59"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 18:12:20"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 18:12:20"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-07-31"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-07-31 18:12:20"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-07-31 18:12:20"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:00"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:00"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:00"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:00"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:09"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:09"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:09"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 08:37:09"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 15:01:02"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 15:01:02"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 15:01:02"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 15:01:02"}
{"level":"error","message":"Authentication error: Token expired","service":"sms-backend","stack":"Error: Token expired\n    at verifyAccessToken (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\utils\\jwt.js:61:13)\n    at authenticate (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\middleware\\auth.js:22:21)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at newFn (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express-async-errors\\index.js:16:20)\n    at Layer.handle [as handle_request] (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)","timestamp":"2025-08-01 15:07:40"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 15:07:55"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 15:07:55"}
{"error":"Unknown column 'date' in 'where clause'","level":"error","message":"Database query error:","params":["2025-08-01"],"query":"\n        SELECT\n          COUNT(*) as total_records,\n          SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as present_count,\n          SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absent_count,\n          SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as late_count\n        FROM attendance\n        WHERE date = ?\n      ","service":"sms-backend","timestamp":"2025-08-01 15:07:56"}
{"error":"Unknown column 'type' in 'field list'","level":"error","message":"Database query error:","params":[],"query":"\n        SELECT title, start_date, start_time, type, location\n        FROM events\n        WHERE start_date >= CURDATE() AND status = 'active'\n        ORDER BY start_date ASC, start_time ASC\n        LIMIT 5\n      ","service":"sms-backend","timestamp":"2025-08-01 15:07:56"}
{"error":"Table 'school_management_system.student_results' doesn't exist","level":"error","message":"Database query error:","params":["2"],"query":"SELECT COUNT(*) as count FROM student_results WHERE student_id = ?","service":"sms-backend","timestamp":"2025-08-01 15:08:10"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Delete student error: Table 'school_management_system.student_results' doesn't exist","service":"sms-backend","sql":"SELECT COUNT(*) as count FROM student_results WHERE student_id = ?","sqlMessage":"Table 'school_management_system.student_results' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.student_results' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:31)\n    at deleteStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1606:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-01 15:08:10"}
{"error":"Table 'school_management_system.student_results' doesn't exist","level":"error","message":"Database query error:","params":["2"],"query":"SELECT COUNT(*) as count FROM student_results WHERE student_id = ?","service":"sms-backend","timestamp":"2025-08-01 15:08:37"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Delete student error: Table 'school_management_system.student_results' doesn't exist","service":"sms-backend","sql":"SELECT COUNT(*) as count FROM student_results WHERE student_id = ?","sqlMessage":"Table 'school_management_system.student_results' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.student_results' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:31)\n    at deleteStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1606:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-01 15:08:37"}
{"error":"Table 'school_management_system.student_results' doesn't exist","level":"error","message":"Database query error:","params":["2"],"query":"SELECT COUNT(*) as count FROM student_results WHERE student_id = ?","service":"sms-backend","timestamp":"2025-08-01 15:26:49"}
{"code":"ER_NO_SUCH_TABLE","errno":1146,"level":"error","message":"Delete student error: Table 'school_management_system.student_results' doesn't exist","service":"sms-backend","sql":"SELECT COUNT(*) as count FROM student_results WHERE student_id = ?","sqlMessage":"Table 'school_management_system.student_results' doesn't exist","sqlState":"42S02","stack":"Error: Table 'school_management_system.student_results' doesn't exist\n    at PromisePool.execute (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\node_modules\\mysql2\\lib\\promise\\pool.js:54:22)\n    at executeQuery (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\config\\database.js:63:31)\n    at deleteStudent (C:\\Users\\<USER>\\Documents\\ORANGE\\PROJECT\\A I projects\\sms\\backend\\src\\controllers\\studentController.js:1606:28)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)","timestamp":"2025-08-01 15:26:49"}
