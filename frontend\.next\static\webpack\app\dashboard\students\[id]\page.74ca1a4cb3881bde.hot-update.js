"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/[id]/page.tsx":
/*!**********************************************!*\
  !*** ./app/dashboard/students/[id]/page.tsx ***!
  \**********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentDetailsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,ClipboardCheck,FileText,Pencil,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,ClipboardCheck,FileText,Pencil,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,ClipboardCheck,FileText,Pencil,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,ClipboardCheck,FileText,Pencil,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,ClipboardCheck,FileText,Pencil,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,ClipboardCheck,FileText,Pencil,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clipboard-check.js\");\n/* harmony import */ var _src_lib_api__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/src/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _src_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/src/contexts/AuthContext */ \"(app-pages-browser)/./src/contexts/AuthContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentDetailsPage(param) {\n    let { params } = param;\n    var _student_first_name, _student_last_name, _student_user_status, _student_user_status1, _student_gender, _student_gender1;\n    _s();\n    // Unwrap params using React.use()\n    const resolvedParams = (0,react__WEBPACK_IMPORTED_MODULE_1__.use)(params);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const { user, isAuthenticated, isLoading: authLoading } = (0,_src_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth)();\n    const [studentData, setStudentData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [academicData, setAcademicData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentDetailsPage.useEffect\": ()=>{\n            const fetchStudent = {\n                \"StudentDetailsPage.useEffect.fetchStudent\": async ()=>{\n                    try {\n                        setLoading(true);\n                        setError(null);\n                        console.log('Auth status:', {\n                            isAuthenticated,\n                            authLoading,\n                            user\n                        });\n                        console.log('Token in localStorage:', localStorage.getItem('auth_token'));\n                        console.log('Fetching student with ID:', params.id);\n                        const response = await _src_lib_api__WEBPACK_IMPORTED_MODULE_10__.studentsApi.getById(params.id);\n                        console.log('Student API response:', response);\n                        // Debug logging\n                        console.log('=== DEBUGGING STUDENT DATA ===');\n                        console.log('Response success:', response.success);\n                        console.log('Response data:', response.data);\n                        if (response.data) {\n                            console.log('Student object:', response.data.student);\n                            console.log('Parents array:', response.data.parents);\n                            if (response.data.student) {\n                                console.log('Student ID:', response.data.student.id, typeof response.data.student.id);\n                                console.log('Student first_name:', response.data.student.first_name);\n                                console.log('Student last_name:', response.data.student.last_name);\n                                console.log('Student full_name:', response.data.student.full_name);\n                                console.log('Student email:', response.data.student.email);\n                                console.log('Student user_status:', response.data.student.user_status);\n                            }\n                        }\n                        if (response.success && response.data) {\n                            console.log('Setting student data:', response.data);\n                            console.log('Data has student:', !!response.data.student);\n                            console.log('Data has parents:', !!response.data.parents);\n                            // Clear any existing error first\n                            setError(null);\n                            // Set the student data\n                            setStudentData(response.data);\n                            console.log('Student data set successfully');\n                            // Verify the data was set (this will trigger in the next render)\n                            setTimeout({\n                                \"StudentDetailsPage.useEffect.fetchStudent\": ()=>{\n                                    console.log('Verification - studentData after setState:', studentData);\n                                }\n                            }[\"StudentDetailsPage.useEffect.fetchStudent\"], 100);\n                        } else {\n                            console.log('Failed to load student data - response not successful or no data');\n                            console.log('Response success:', response.success);\n                            console.log('Response data:', response.data);\n                            setError('Failed to load student data');\n                        }\n                    } catch (error) {\n                        console.error('Error fetching student:', error);\n                        setError('Failed to load student data');\n                        toast({\n                            title: \"Error\",\n                            description: \"Failed to load student information\",\n                            variant: \"destructive\"\n                        });\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"StudentDetailsPage.useEffect.fetchStudent\"];\n            if (params.id && !authLoading) {\n                if (!isAuthenticated) {\n                    console.log('User not authenticated, redirecting to login');\n                    router.push('/login');\n                    return;\n                }\n                fetchStudent();\n            }\n        }\n    }[\"StudentDetailsPage.useEffect\"], [\n        params.id,\n        toast,\n        isAuthenticated,\n        authLoading,\n        router\n    ]);\n    const handleEditStudent = async (updatedStudent)=>{\n        try {\n            await _src_lib_api__WEBPACK_IMPORTED_MODULE_10__.studentsApi.update(updatedStudent.id, updatedStudent);\n            // Update local state\n            if (studentData) {\n                setStudentData({\n                    ...studentData,\n                    student: updatedStudent\n                });\n            }\n            toast({\n                title: \"Success\",\n                description: \"Student information updated successfully\"\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student information\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async ()=>{\n        try {\n            if (!(studentData === null || studentData === void 0 ? void 0 : studentData.student)) return;\n            await _src_lib_api__WEBPACK_IMPORTED_MODULE_10__.studentsApi.delete(studentData.student.id);\n            toast({\n                title: \"Success\",\n                description: \"Student deleted successfully\"\n            });\n            // Navigate back to students list\n            router.push(\"/dashboard/students\");\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    if (authLoading || loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-gray-600\",\n                        children: authLoading ? 'Checking authentication...' : 'Loading student details...'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 219,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                lineNumber: 217,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this);\n    }\n    if (error || !(studentData === null || studentData === void 0 ? void 0 : studentData.student)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-semibold text-gray-900\",\n                        children: \"Student Not Found\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2 text-sm text-gray-600\",\n                        children: error || 'The requested student could not be found.'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 234,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push('/dashboard/students'),\n                        className: \"mt-4\",\n                        children: \"Back to Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 236,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                lineNumber: 232,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n            lineNumber: 231,\n            columnNumber: 7\n        }, this);\n    }\n    const { student, parents } = studentData;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                size: \"icon\",\n                                onClick: ()=>router.push(\"/dashboard/students\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 254,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 253,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"Student Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 256,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 252,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_8__.EditStudentModal, {\n                                student: student,\n                                onSave: handleEditStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 264,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Edit Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 263,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 259,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_9__.DeleteConfirmationModal, {\n                                title: \"Delete Student\",\n                                description: \"Are you sure you want to delete \".concat(student.full_name || \"\".concat(student.first_name, \" \").concat(student.last_name), \"? This action cannot be undone.\"),\n                                onConfirm: handleDeleteStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"destructive\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 275,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Delete Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 258,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                lineNumber: 251,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid gap-6 md:grid-cols-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"md:col-span-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Profile\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"Student personal information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"flex flex-col items-center text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                                        className: \"h-32 w-32\",\n                                        children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                                            src: student.profile_picture || \"/placeholder.svg\",\n                                            alt: student.full_name\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 293,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                                            className: \"text-4xl\",\n                                            children: [\n                                                (_student_first_name = student.first_name) === null || _student_first_name === void 0 ? void 0 : _student_first_name.charAt(0),\n                                                (_student_last_name = student.last_name) === null || _student_last_name === void 0 ? void 0 : _student_last_name.charAt(0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 295,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"mt-4 text-xl font-bold\",\n                                        children: student.full_name || \"\".concat(student.first_name, \" \").concat(student.last_name)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: [\n                                            \"Student ID: \",\n                                            student.student_id\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                        className: \"mt-2\",\n                                        variant: student.user_status === \"active\" ? \"default\" : \"outline\",\n                                        children: ((_student_user_status = student.user_status) === null || _student_user_status === void 0 ? void 0 : _student_user_status.charAt(0).toUpperCase()) + ((_student_user_status1 = student.user_status) === null || _student_user_status1 === void 0 ? void 0 : _student_user_status1.slice(1)) || \"Unknown\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 w-full space-y-2 text-left\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Class:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 307,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: student.class_name || \"Not Assigned\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 308,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Grade Level:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 311,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: student.grade_level || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Gender:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 315,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: ((_student_gender = student.gender) === null || _student_gender === void 0 ? void 0 : _student_gender.charAt(0).toUpperCase()) + ((_student_gender1 = student.gender) === null || _student_gender1 === void 0 ? void 0 : _student_gender1.slice(1)) || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 316,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 314,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Date of Birth:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Blood Group:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: student.blood_group || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Admission Date:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: student.admission_date ? new Date(student.admission_date).toLocaleDateString() : \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 328,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 326,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Religion:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: student.religion || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between border-b pb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Nationality:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: student.nationality || \"N/A\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 336,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 334,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 290,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:col-span-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                            defaultValue: \"contact\",\n                            className: \"w-full\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                                    className: \"grid w-full grid-cols-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                            value: \"contact\",\n                                            children: \"Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                            value: \"academic\",\n                                            children: \"Academic\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                            value: \"attendance\",\n                                            children: \"Attendance\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                    value: \"contact\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        children: \"Contact Information\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 353,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                        children: \"Student and parent/guardian contact details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 352,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid gap-4 md:grid-cols-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"mb-2 font-medium\",\n                                                                        children: \"Student Contact\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 359,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between border-b pb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Email:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 362,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: student.email || \"N/A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 363,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 361,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between border-b pb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Phone:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 366,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: student.phone || \"N/A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 367,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 365,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex justify-between border-b pb-2\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: \"Address:\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 370,\n                                                                                        columnNumber: 27\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        children: student.address || \"N/A\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 371,\n                                                                                        columnNumber: 27\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 369,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 360,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"mb-2 font-medium\",\n                                                                        children: \"Parent/Guardian Contact\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"space-y-2\",\n                                                                        children: parents && parents.length > 0 ? parents.map((parent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"mb-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center gap-2 mb-2\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n                                                                                                className: \"font-medium\",\n                                                                                                children: [\n                                                                                                    parent.first_name,\n                                                                                                    \" \",\n                                                                                                    parent.last_name\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 382,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            parent.is_primary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                                                                variant: \"secondary\",\n                                                                                                className: \"text-xs\",\n                                                                                                children: \"Primary\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 383,\n                                                                                                columnNumber: 55\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 381,\n                                                                                        columnNumber: 31\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"space-y-1 text-sm\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex justify-between border-b pb-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Relationship:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 387,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: parent.relationship\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 388,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 386,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex justify-between border-b pb-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Email:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 391,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: parent.email\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 392,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 390,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex justify-between border-b pb-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Phone:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 395,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: parent.phone || \"N/A\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 396,\n                                                                                                        columnNumber: 35\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 394,\n                                                                                                columnNumber: 33\n                                                                                            }, this),\n                                                                                            parent.work_phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex justify-between border-b pb-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Work Phone:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 400,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: parent.work_phone\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 401,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 399,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            parent.occupation && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                className: \"flex justify-between border-b pb-1\",\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: \"Occupation:\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 406,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                                        children: parent.occupation\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                        lineNumber: 407,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                                lineNumber: 405,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                        lineNumber: 385,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, parent.id, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 380,\n                                                                                columnNumber: 29\n                                                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"No parent information available\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 414,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 377,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 357,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Emergency Contact\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between border-b pb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Name:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 423,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: student.emergency_contact_name || \"N/A\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 424,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 422,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between border-b pb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Phone:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 427,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: student.emergency_contact_phone || \"N/A\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 428,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 426,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between border-b pb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"Relationship:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 431,\n                                                                                columnNumber: 25\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: student.emergency_contact_relation || \"N/A\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 432,\n                                                                                columnNumber: 25\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 430,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"mb-2 font-medium\",\n                                                                children: \"Medical Information\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 437,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex justify-between border-b pb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: \"Medical Conditions:\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 440,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            children: student.medical_conditions || \"None\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 441,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 439,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 438,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 436,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 356,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Print Contact Information\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 351,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                    value: \"academic\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        children: \"Academic Performance\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 457,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                        children: \"Current grades and academic standing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 458,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 456,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"mx-auto h-12 w-12 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 463,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mt-4 text-lg font-semibold\",\n                                                                    children: \"Academic Data Not Available\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 464,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-2 text-sm text-muted-foreground\",\n                                                                    children: \"Academic performance data will be available once grades are entered into the system.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 465,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 462,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid gap-4 md:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"rounded-md border p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"mb-2 font-medium\",\n                                                                            children: \"Academic Year\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 471,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold\",\n                                                                            children: student.academic_year || \"N/A\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Current academic year\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 473,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 470,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"rounded-md border p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"mb-2 font-medium\",\n                                                                            children: \"Admission Number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 476,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold\",\n                                                                            children: student.admission_number || \"N/A\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 477,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Student admission number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 478,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 475,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 469,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 461,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 460,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    disabled: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Generate Academic Report (Coming Soon)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 454,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                                    value: \"attendance\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                        children: \"Attendance Record\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 494,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                        children: \"Student attendance history and statistics\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"mx-auto h-12 w-12 text-muted-foreground\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 500,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"mt-4 text-lg font-semibold\",\n                                                                    children: \"Attendance Data Not Available\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 501,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"mt-2 text-sm text-muted-foreground\",\n                                                                    children: \"Attendance records will be available once the attendance system is integrated.\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 502,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 499,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid gap-4 md:grid-cols-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"rounded-md border p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"mb-2 font-medium\",\n                                                                            children: \"Roll Number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 508,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold\",\n                                                                            children: student.roll_number || \"N/A\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 509,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Student roll number\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 510,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 507,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"rounded-md border p-4\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"mb-2 font-medium\",\n                                                                            children: \"Last Login\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 513,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-lg font-bold\",\n                                                                            children: student.last_login ? new Date(student.last_login).toLocaleDateString() : \"Never\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 514,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"text-sm text-muted-foreground\",\n                                                                            children: \"Last system access\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 517,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 512,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 506,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 498,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 497,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"w-full\",\n                                                    disabled: true,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 524,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"View Full Attendance Calendar (Coming Soon)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 523,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                            lineNumber: 344,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 343,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"md:col-span-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Upcoming Assignments\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 536,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"Pending assignments and due dates\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Mathematics Problem Set\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 543,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Due: Oct 25, 2023\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 544,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 542,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    children: \"Pending\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 546,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 541,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"English Literature Essay\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 550,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Due: Oct 30, 2023\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 551,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 549,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    children: \"Pending\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 553,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 548,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Science Lab Report\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 557,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Due: Nov 5, 2023\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 558,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 556,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                    children: \"Pending\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 560,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 555,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 540,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"md:col-span-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        children: \"Recent Activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 568,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                        children: \"Student's recent academic activities\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 569,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 567,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-8 w-8 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 574,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Completed History Quiz\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 576,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Oct 18, 2023 - Score: 92%\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 577,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 575,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                    className: \"h-8 w-8 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 581,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Submitted Science Assignment\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 583,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Oct 15, 2023\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 584,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 582,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 580,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_ClipboardCheck_FileText_Pencil_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-8 w-8 text-muted-foreground\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 588,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium\",\n                                                            children: \"Participated in Math Competition\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 590,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-muted-foreground\",\n                                                            children: \"Oct 10, 2023\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 591,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 589,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 572,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\[id]\\\\page.tsx\",\n        lineNumber: 250,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentDetailsPage, \"iw1+iDBrG9wXVJJiMy4pp8hz6j4=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast,\n        _src_contexts_AuthContext__WEBPACK_IMPORTED_MODULE_12__.useAuth\n    ];\n});\n_c = StudentDetailsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentDetailsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/[id]/page.tsx\n"));

/***/ })

});