# Student Dashboard API Testing Guide

## 🧪 Testing Overview

This guide provides comprehensive testing instructions for all student dashboard API endpoints. Each endpoint includes sample requests, expected responses, and error scenarios.

## 🔐 Authentication Setup

### Prerequisites
1. **JWT Token**: Obtain a valid JWT token for a student user
2. **Base URL**: `http://localhost:5000/api`
3. **Headers**: Include `Authorization: Bearer <jwt_token>` in all requests

### Sample Authentication Header
```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## 📊 API Endpoint Tests

### 1. Student Dashboard Overview

#### **GET /api/student-portal/dashboard**

**Purpose**: Get comprehensive dashboard data for the authenticated student

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/dashboard" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Student dashboard data retrieved successfully",
  "data": {
    "student": {
      "id": 1,
      "student_id": "STU-20240001",
      "first_name": "John",
      "last_name": "Doe",
      "class_name": "Grade 10-A",
      "grade_level": "10",
      "academic_year": "2024-2025"
    },
    "attendance": {
      "total_days": 20,
      "present_days": 18,
      "absent_days": 2,
      "late_days": 0,
      "attendance_percentage": 90.00
    },
    "recent_grades": [
      {
        "subject_name": "Mathematics",
        "marks_obtained": 85,
        "total_marks": 100,
        "grade": "A",
        "exam_date": "2024-01-15"
      }
    ],
    "fees": {
      "total_fees": 5000,
      "paid_amount": 3000,
      "pending_amount": 2000,
      "overdue_count": 0
    },
    "upcoming_events": [],
    "unread_messages": 2
  }
}
```

### 2. Student Profile Management

#### **GET /api/student-portal/profile**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/profile" \
  -H "Authorization: Bearer <jwt_token>"
```

#### **PUT /api/student-portal/profile**

**Sample Request**:
```bash
curl -X PUT "http://localhost:5000/api/student-portal/profile" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+**********",
    "address": "123 Main Street, City, State",
    "emergency_contact_name": "Jane Doe",
    "emergency_contact_phone": "+**********",
    "emergency_contact_relation": "Mother",
    "medical_conditions": "No known allergies"
  }'
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Profile updated successfully"
}
```

### 3. Attendance Records

#### **GET /api/student-portal/attendance**

**Sample Request with Filters**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/attendance?page=1&limit=10&month=1&year=2024" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Attendance records retrieved successfully",
  "data": [
    {
      "id": 1,
      "attendance_date": "2024-01-15",
      "period_number": 1,
      "status": "present",
      "check_in_time": "08:00:00",
      "subject_name": "Mathematics",
      "teacher_name": "Dr. Smith"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 50,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "statistics": {
    "total_days": 20,
    "present_days": 18,
    "absent_days": 2,
    "late_days": 0,
    "attendance_percentage": 90.00
  }
}
```

### 4. Grades and Results

#### **GET /api/student-portal/grades**

**Sample Request with Filters**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/grades?page=1&limit=10&assessment_type=exam" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Grades retrieved successfully",
  "data": [
    {
      "id": 1,
      "marks_obtained": 85,
      "total_marks": 100,
      "grade": "A",
      "percentage": 85.0,
      "exam_date": "2024-01-15",
      "subject_name": "Mathematics",
      "assessment_name": "Mid-term Exam",
      "assessment_type": "exam",
      "teacher_name": "Dr. Smith"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 3,
    "totalItems": 25,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "statistics": {
    "total_assessments": 25,
    "average_percentage": 82.5,
    "highest_percentage": 95.0,
    "lowest_percentage": 65.0,
    "excellent_grades": 8,
    "good_grades": 12,
    "average_grades": 5,
    "below_average_grades": 0
  }
}
```

### 5. Fee Records

#### **GET /api/student-portal/fees**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/fees?page=1&limit=10&payment_status=pending" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Fee records retrieved successfully",
  "data": [
    {
      "id": 1,
      "fee_type": "Tuition Fee",
      "amount": 2000,
      "due_date": "2024-02-01",
      "payment_status": "pending",
      "payment_date": null,
      "academic_year": "2024-2025"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 2,
    "totalItems": 15,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "summary": {
    "total_fees": 5000,
    "paid_amount": 3000,
    "pending_amount": 2000,
    "overdue_amount": 0,
    "total_late_fees": 0,
    "total_discounts": 0,
    "overdue_count": 0
  }
}
```

### 6. Class Timetable

#### **GET /api/student-portal/timetable**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/timetable" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Timetable retrieved successfully",
  "data": {
    "class_name": "Grade 10-A",
    "timetable": {
      "id": 1,
      "timetable_name": "Grade 10-A Timetable",
      "effective_from": "2024-01-01",
      "status": "active"
    },
    "periods": {
      "Monday": [
        {
          "period_number": 1,
          "start_time": "08:00:00",
          "end_time": "08:45:00",
          "subject_name": "Mathematics",
          "teacher_name": "Dr. Smith",
          "room_number": "101"
        }
      ],
      "Tuesday": [
        {
          "period_number": 1,
          "start_time": "08:00:00",
          "end_time": "08:45:00",
          "subject_name": "English",
          "teacher_name": "Ms. Johnson",
          "room_number": "102"
        }
      ]
    }
  }
}
```

### 7. Student Subjects

#### **GET /api/student-portal/subjects**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/subjects" \
  -H "Authorization: Bearer <jwt_token>"
```

### 8. Student Events

#### **GET /api/student-portal/events**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/student-portal/events?page=1&limit=10&upcoming_only=true" \
  -H "Authorization: Bearer <jwt_token>"
```

## ❌ Error Scenarios

### 1. Authentication Errors

**Missing Token**:
```json
{
  "success": false,
  "message": "Access token required"
}
```

**Invalid Token**:
```json
{
  "success": false,
  "message": "Invalid or expired token"
}
```

**Wrong User Type**:
```json
{
  "success": false,
  "message": "Access denied. Student role required."
}
```

### 2. Validation Errors

**Invalid Query Parameters**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "page",
      "message": "Page must be a positive integer"
    }
  ]
}
```

### 3. Resource Not Found

**Student Record Not Found**:
```json
{
  "success": false,
  "message": "Student record not found"
}
```

## 🧪 Testing Checklist

### Functional Testing
- [ ] All endpoints return correct data for authenticated student
- [ ] Pagination works correctly on all paginated endpoints
- [ ] Filtering parameters work as expected
- [ ] Profile update only allows permitted fields
- [ ] Statistics calculations are accurate

### Security Testing
- [ ] Endpoints reject requests without authentication
- [ ] Students cannot access other students' data
- [ ] Input validation prevents malicious input
- [ ] SQL injection attempts are blocked
- [ ] Rate limiting works correctly

### Performance Testing
- [ ] Response times are under 500ms for simple queries
- [ ] Large datasets are properly paginated
- [ ] Database queries are optimized
- [ ] Memory usage is reasonable

### Error Handling Testing
- [ ] Invalid parameters return appropriate error messages
- [ ] Database errors are handled gracefully
- [ ] Network timeouts are handled properly
- [ ] Malformed requests return 400 status codes

## 🔧 Testing Tools

### Recommended Tools
- **Postman**: For manual API testing
- **curl**: For command-line testing
- **Jest + Supertest**: For automated testing
- **Artillery**: For load testing

### Sample Postman Collection
Import the following collection for comprehensive testing:
```json
{
  "info": {
    "name": "Student Dashboard API Tests",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{jwt_token}}",
        "type": "string"
      }
    ]
  }
}
```

This comprehensive testing guide ensures all student dashboard API endpoints are thoroughly tested for functionality, security, and performance.
