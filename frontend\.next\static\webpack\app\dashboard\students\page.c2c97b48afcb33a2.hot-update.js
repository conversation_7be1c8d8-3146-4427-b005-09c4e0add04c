"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.update(updatedStudent.id, updatedStudent);\n            setStudents((prev)=>prev.map((student)=>student.id === updatedStudent.id ? updatedStudent : student));\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(id);\n            setStudents((prev)=>prev.filter((student)=>student.id !== id));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(id)));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(student.id)));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id,\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender,\n                    'Date of Birth': student.date_of_birth,\n                    'Guardian': student.guardian_name,\n                    'Guardian Phone': student.guardian_phone,\n                    'Class': student.class_name || '',\n                    'Status': student.status,\n                    'Admission Date': student.admission_date\n                }));\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        toast({\n            title: \"Import Feature\",\n            description: \"Student import functionality will be implemented with file upload.\"\n        });\n    };\n    const handlePrintAll = ()=>{\n        window.print();\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 243,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 250,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 273,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 269,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_name\",\n            header: \"Parent/Guardian\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardian = row.getValue(\"guardian_name\");\n                return guardian || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_phone\",\n            header: \"Guardian Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardianPhone = row.getValue(\"guardian_phone\");\n                return guardianPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 352,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 361,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 380,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__.EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 388,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 399,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 393,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 413,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 411,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 435,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 434,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 441,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 438,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 454,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 456,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 450,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 473,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 464,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 422,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9kYXNoYm9hcmQvc3R1ZGVudHMvcGFnZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTJDO0FBQ0E7QUFDSTtBQUNpRDtBQUM3QztBQUN5QjtBQUMvQjtBQUNTO0FBRW1CO0FBQ2M7QUFDUztBQUN6QjtBQUNVO0FBQ047QUFDL0I7QUFDTDtBQUMrRDtBQW9DdkYsU0FBU2dDOztJQUN0QixNQUFNQyxTQUFTL0IsMERBQVNBO0lBQ3hCLE1BQU0sRUFBRWdDLEtBQUssRUFBRSxHQUFHYiwyREFBUUE7SUFDMUIsTUFBTSxDQUFDYyxVQUFVQyxZQUFZLEdBQUdwQywrQ0FBUUEsQ0FBWSxFQUFFO0lBQ3RELE1BQU0sQ0FBQ3FDLGNBQWNDLGdCQUFnQixHQUFHdEMsK0NBQVFBLENBQVcsRUFBRTtJQUM3RCxNQUFNLENBQUN1QyxTQUFTQyxXQUFXLEdBQUd4QywrQ0FBUUEsQ0FBQztJQUN2QyxNQUFNLENBQUN5QyxZQUFZQyxjQUFjLEdBQUcxQywrQ0FBUUEsQ0FBQztRQUMzQzJDLGFBQWE7UUFDYkMsWUFBWTtRQUNaQyxZQUFZO1FBQ1pDLGNBQWM7SUFDaEI7SUFJQSxNQUFNQyxnQkFBZ0I7WUFBT0Msd0VBQU8sR0FBR0MseUVBQVEsSUFBSUMsMEVBQVM7UUFDMUQsSUFBSTtnQkFZVUMsZ0JBQ1JBO1lBWkpYLFdBQVc7WUFDWCxNQUFNVyxXQUFXLE1BQU03QixrREFBV0EsQ0FBQzhCLE1BQU0sQ0FBQztnQkFDeENKO2dCQUNBQztnQkFDQUM7Z0JBQ0FHLFNBQVM7Z0JBQ1RDLFlBQVk7WUFDZDtZQUlBbEIsWUFBWWUsRUFBQUEsaUJBQUFBLFNBQVNJLElBQUksY0FBYkoscUNBQUFBLGVBQWVoQixRQUFRLEtBQUksRUFBRTtZQUN6QyxLQUFJZ0Isa0JBQUFBLFNBQVNJLElBQUksY0FBYkosc0NBQUFBLGdCQUFlVixVQUFVLEVBQUU7Z0JBQzdCQyxjQUFjUyxTQUFTSSxJQUFJLENBQUNkLFVBQVU7WUFDeEM7UUFDRixFQUFFLE9BQU9lLE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7WUFDMUN0QixNQUFNO2dCQUNKd0IsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsU0FBUztZQUNYO1FBQ0YsU0FBVTtZQUNScEIsV0FBVztRQUNiO0lBQ0Y7SUFFQXZDLGdEQUFTQTtrQ0FBQztZQUNSOEM7UUFDRjtpQ0FBRyxFQUFFO0lBRUwsTUFBTWMsb0JBQW9CLE9BQU9DO1FBQy9CLElBQUk7WUFDRixNQUFNeEMsa0RBQVdBLENBQUN5QyxNQUFNLENBQUNELGVBQWVFLEVBQUUsRUFBRUY7WUFDNUMxQixZQUFZLENBQUM2QixPQUFTQSxLQUFLQyxHQUFHLENBQUMsQ0FBQ0MsVUFBYUEsUUFBUUgsRUFBRSxLQUFLRixlQUFlRSxFQUFFLEdBQUdGLGlCQUFpQks7WUFDakdqQyxNQUFNO2dCQUNKd0IsT0FBTztnQkFDUEMsYUFBYSxHQUFnQ0csT0FBN0JBLGVBQWVNLFVBQVUsRUFBQyxLQUE0QixPQUF6Qk4sZUFBZU8sU0FBUyxFQUFDO1lBQ3hFO1FBQ0YsRUFBRSxPQUFPYixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1lBQ3pDdEIsTUFBTTtnQkFDSndCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNVSxzQkFBc0IsT0FBT047UUFDakMsSUFBSTtZQUNGLE1BQU0xQyxrREFBV0EsQ0FBQ2lELE1BQU0sQ0FBQ1A7WUFDekI1QixZQUFZLENBQUM2QixPQUFTQSxLQUFLTyxNQUFNLENBQUMsQ0FBQ0wsVUFBWUEsUUFBUUgsRUFBRSxLQUFLQTtZQUM5RDlCLE1BQU07Z0JBQ0p3QixPQUFPO2dCQUNQQyxhQUFhO1lBQ2Y7UUFDRixFQUFFLE9BQU9ILE9BQU87WUFDZEMsUUFBUUQsS0FBSyxDQUFDLDJCQUEyQkE7WUFDekN0QixNQUFNO2dCQUNKd0IsT0FBTztnQkFDUEMsYUFBYTtnQkFDYkMsU0FBUztZQUNYO1FBQ0Y7SUFDRjtJQUVBLE1BQU1hLG1CQUFtQixPQUFPQztRQUM5QixJQUFJO1lBQ0YsTUFBTXZCLFdBQVcsTUFBTTdCLGtEQUFXQSxDQUFDcUQsTUFBTSxDQUFDRDtZQUUxQyxtREFBbUQ7WUFDbkQsTUFBTTNCO1lBRU5iLE1BQU07Z0JBQ0p3QixPQUFPO2dCQUNQQyxhQUFhLEdBQTJCZSxPQUF4QkEsV0FBV0UsU0FBUyxFQUFDLEtBQXVCLE9BQXBCRixXQUFXRyxRQUFRLEVBQUM7WUFDOUQ7UUFDRixFQUFFLE9BQU9yQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1lBQ3ZDdEIsTUFBTTtnQkFDSndCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNa0IsbUJBQW1CO1FBQ3ZCLElBQUk7WUFDRix1RUFBdUU7WUFDdkUsTUFBTUMsUUFBUUMsR0FBRyxDQUFDM0MsYUFBYTZCLEdBQUcsQ0FBQ0YsQ0FBQUEsS0FBTTFDLGtEQUFXQSxDQUFDaUQsTUFBTSxDQUFDUDtZQUM1RDVCLFlBQVksQ0FBQzZCLE9BQVNBLEtBQUtPLE1BQU0sQ0FBQyxDQUFDTCxVQUFZLENBQUM5QixhQUFhNEMsUUFBUSxDQUFDZCxRQUFRSCxFQUFFO1lBQ2hGOUIsTUFBTTtnQkFDSndCLE9BQU87Z0JBQ1BDLGFBQWEsR0FBdUIsT0FBcEJ0QixhQUFhNkMsTUFBTSxFQUFDO1lBQ3RDO1lBQ0E1QyxnQkFBZ0IsRUFBRTtRQUNwQixFQUFFLE9BQU9rQixPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyw0QkFBNEJBO1lBQzFDdEIsTUFBTTtnQkFDSndCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNdUIsdUJBQXVCO1FBQzNCLElBQUk7WUFDRixzQkFBc0I7WUFDdEIsTUFBTUMsVUFBVWpELFNBQVMrQixHQUFHLENBQUNDLENBQUFBLFVBQVk7b0JBQ3ZDLGNBQWNBLFFBQVFrQixVQUFVO29CQUNoQyxRQUFRLEdBQXlCbEIsT0FBdEJBLFFBQVFDLFVBQVUsRUFBQyxLQUFxQixPQUFsQkQsUUFBUUUsU0FBUztvQkFDbEQsU0FBU0YsUUFBUW1CLEtBQUssSUFBSTtvQkFDMUIsU0FBU25CLFFBQVFvQixLQUFLLElBQUk7b0JBQzFCLFVBQVVwQixRQUFRcUIsTUFBTTtvQkFDeEIsaUJBQWlCckIsUUFBUXNCLGFBQWE7b0JBQ3RDLFlBQVl0QixRQUFRdUIsYUFBYTtvQkFDakMsa0JBQWtCdkIsUUFBUXdCLGNBQWM7b0JBQ3hDLFNBQVN4QixRQUFReUIsVUFBVSxJQUFJO29CQUMvQixVQUFVekIsUUFBUTBCLE1BQU07b0JBQ3hCLGtCQUFrQjFCLFFBQVEyQixjQUFjO2dCQUMxQztZQUVBLE1BQU1DLGFBQWE7Z0JBQ2pCQyxPQUFPQyxJQUFJLENBQUNiLE9BQU8sQ0FBQyxFQUFFLEVBQUVjLElBQUksQ0FBQzttQkFDMUJkLFFBQVFsQixHQUFHLENBQUNpQyxDQUFBQSxNQUFPSCxPQUFPSSxNQUFNLENBQUNELEtBQUtELElBQUksQ0FBQzthQUMvQyxDQUFDQSxJQUFJLENBQUM7WUFFUCxNQUFNRyxPQUFPLElBQUlDLEtBQUs7Z0JBQUNQO2FBQVcsRUFBRTtnQkFBRVEsTUFBTTtZQUFXO1lBQ3ZELE1BQU1DLE1BQU1DLE9BQU9DLEdBQUcsQ0FBQ0MsZUFBZSxDQUFDTjtZQUN2QyxNQUFNTyxJQUFJQyxTQUFTQyxhQUFhLENBQUM7WUFDakNGLEVBQUVHLElBQUksR0FBR1A7WUFDVEksRUFBRUksUUFBUSxHQUFHLFlBQW1ELE9BQXZDLElBQUlDLE9BQU9DLFdBQVcsR0FBR0MsS0FBSyxDQUFDLElBQUksQ0FBQyxFQUFFLEVBQUM7WUFDaEVQLEVBQUVRLEtBQUs7WUFDUFgsT0FBT0MsR0FBRyxDQUFDVyxlQUFlLENBQUNiO1lBRTNCdEUsTUFBTTtnQkFDSndCLE9BQU87Z0JBQ1BDLGFBQWE7WUFDZjtRQUNGLEVBQUUsT0FBT0gsT0FBTztZQUNkdEIsTUFBTTtnQkFDSndCLE9BQU87Z0JBQ1BDLGFBQWE7Z0JBQ2JDLFNBQVM7WUFDWDtRQUNGO0lBQ0Y7SUFFQSxNQUFNMEQsdUJBQXVCO1FBQzNCcEYsTUFBTTtZQUNKd0IsT0FBTztZQUNQQyxhQUFhO1FBQ2Y7SUFDRjtJQUVBLE1BQU00RCxpQkFBaUI7UUFDckJkLE9BQU9lLEtBQUs7UUFDWnRGLE1BQU07WUFDSndCLE9BQU87WUFDUEMsYUFBYTtRQUNmO0lBQ0Y7SUFFQSxNQUFNOEQsVUFBZ0M7UUFDcEM7WUFDRXpELElBQUk7WUFDSjBELFFBQVE7b0JBQUMsRUFBRUMsS0FBSyxFQUFFO3FDQUNoQiw4REFBQ2xILDZEQUFRQTtvQkFDUG1ILFNBQVNELE1BQU1FLHdCQUF3QixNQUFPRixNQUFNRyx5QkFBeUIsTUFBTTtvQkFDbkZDLGlCQUFpQixDQUFDQyxRQUFVTCxNQUFNTSx5QkFBeUIsQ0FBQyxDQUFDLENBQUNEO29CQUM5REUsY0FBVzs7Ozs7OztZQUdmQyxNQUFNO29CQUFDLEVBQUVoQyxHQUFHLEVBQUU7cUNBQ1osOERBQUMxRiw2REFBUUE7b0JBQ1BtSCxTQUFTekIsSUFBSWlDLGFBQWE7b0JBQzFCTCxpQkFBaUIsQ0FBQ0MsUUFBVTdCLElBQUlrQyxjQUFjLENBQUMsQ0FBQyxDQUFDTDtvQkFDakRFLGNBQVc7Ozs7Ozs7WUFHZkksZUFBZTtZQUNmQyxjQUFjO1FBQ2hCO1FBQ0E7WUFDRUMsYUFBYTtZQUNiZCxRQUFRO1FBQ1Y7UUFDQTtZQUNFYyxhQUFhO1lBQ2JkLFFBQVE7WUFDUlMsTUFBTTtvQkFBQyxFQUFFaEMsR0FBRyxFQUFFO2dCQUNaLE1BQU1oQyxVQUFVZ0MsSUFBSXNDLFFBQVE7Z0JBQzVCLHFCQUNFLDhEQUFDL0gseURBQU1BO29CQUFDZ0ksV0FBVTs4QkFDZnZFLFFBQVF3RSxlQUFlLGlCQUN0Qiw4REFBQy9ILDhEQUFXQTt3QkFBQ2dJLEtBQUt6RSxRQUFRd0UsZUFBZSxJQUFJO3dCQUFvQkUsS0FBSyxHQUF5QjFFLE9BQXRCQSxRQUFRQyxVQUFVLEVBQUMsS0FBcUIsT0FBbEJELFFBQVFFLFNBQVM7Ozs7OzZDQUVoSCw4REFBQzFELGlFQUFjQTs7NEJBQUV3RCxRQUFRQyxVQUFVLENBQUMwRSxNQUFNLENBQUM7NEJBQUkzRSxRQUFRRSxTQUFTLENBQUN5RSxNQUFNLENBQUM7Ozs7Ozs7Ozs7OztZQUloRjtRQUNGO1FBQ0E7WUFDRU4sYUFBYTtZQUNiZCxRQUFRO1lBQ1JTLE1BQU07b0JBQUMsRUFBRWhDLEdBQUcsRUFBRTtnQkFDWixNQUFNaEMsVUFBVWdDLElBQUlzQyxRQUFRO2dCQUM1QixPQUFPLEdBQXlCdEUsT0FBdEJBLFFBQVFDLFVBQVUsRUFBQyxLQUFxQixPQUFsQkQsUUFBUUUsU0FBUztZQUNuRDtRQUNGO1FBQ0E7WUFDRW1FLGFBQWE7WUFDYmQsUUFBUTtRQUNWO1FBQ0E7WUFDRWMsYUFBYTtZQUNiZCxRQUFRO1lBQ1JTLE1BQU07b0JBQUMsRUFBRWhDLEdBQUcsRUFBRTtnQkFDWixNQUFNYixRQUFRYSxJQUFJNEMsUUFBUSxDQUFDO2dCQUMzQixPQUFPekQsU0FBUztZQUNsQjtRQUNGO1FBQ0E7WUFDRWtELGFBQWE7WUFDYmQsUUFBUTtZQUNSUyxNQUFNO29CQUFDLEVBQUVoQyxHQUFHLEVBQUU7Z0JBQ1osTUFBTVosUUFBUVksSUFBSTRDLFFBQVEsQ0FBQztnQkFDM0IsT0FBT3hELFNBQVM7WUFDbEI7UUFDRjtRQUNBO1lBQ0VpRCxhQUFhO1lBQ2JkLFFBQVE7WUFDUlMsTUFBTTtvQkFBQyxFQUFFaEMsR0FBRyxFQUFFO2dCQUNaLE1BQU1YLFNBQVNXLElBQUk0QyxRQUFRLENBQUM7Z0JBQzVCLE9BQU92RCxTQUFTQSxPQUFPc0QsTUFBTSxDQUFDLEdBQUdFLFdBQVcsS0FBS3hELE9BQU95RCxLQUFLLENBQUMsS0FBSztZQUNyRTtRQUNGO1FBQ0E7WUFDRVQsYUFBYTtZQUNiZCxRQUFRO1lBQ1JTLE1BQU07b0JBQUMsRUFBRWhDLEdBQUcsRUFBRTtnQkFDWixNQUFNK0MsTUFBTS9DLElBQUk0QyxRQUFRLENBQUM7Z0JBQ3pCLE9BQU9HLE1BQU0sSUFBSWpDLEtBQUtpQyxLQUFLQyxrQkFBa0IsS0FBSztZQUNwRDtRQUNGO1FBQ0E7WUFDRVgsYUFBYTtZQUNiZCxRQUFRO1lBQ1JTLE1BQU07b0JBQUMsRUFBRWhDLEdBQUcsRUFBRTtnQkFDWixNQUFNaUQsZ0JBQWdCakQsSUFBSTRDLFFBQVEsQ0FBQztnQkFDbkMsT0FBT0ssZ0JBQWdCLElBQUluQyxLQUFLbUMsZUFBZUQsa0JBQWtCLEtBQUs7WUFDeEU7UUFDRjtRQUNBO1lBQ0VYLGFBQWE7WUFDYmQsUUFBUTtZQUNSUyxNQUFNO29CQUFDLEVBQUVoQyxHQUFHLEVBQUU7Z0JBQ1osTUFBTWtELFdBQVdsRCxJQUFJNEMsUUFBUSxDQUFDO2dCQUM5QixPQUFPTSxZQUFZO1lBQ3JCO1FBQ0Y7UUFDQTtZQUNFYixhQUFhO1lBQ2JkLFFBQVE7WUFDUlMsTUFBTTtvQkFBQyxFQUFFaEMsR0FBRyxFQUFFO2dCQUNaLE1BQU1tRCxnQkFBZ0JuRCxJQUFJNEMsUUFBUSxDQUFDO2dCQUNuQyxPQUFPTyxpQkFBaUI7WUFDMUI7UUFDRjtRQUNBO1lBQ0VkLGFBQWE7WUFDYmQsUUFBUTtZQUNSUyxNQUFNO29CQUFDLEVBQUVoQyxHQUFHLEVBQUU7Z0JBQ1osTUFBTU4sU0FBU00sSUFBSTRDLFFBQVEsQ0FBQztnQkFDNUIscUJBQU8sOERBQUNsSSx1REFBS0E7b0JBQUMrQyxTQUFTaUMsV0FBVyxXQUFXLFlBQVk7OEJBQVlBOzs7Ozs7WUFDdkU7UUFDRjtRQUNBO1lBQ0U3QixJQUFJO1lBQ0ptRSxNQUFNO29CQUFDLEVBQUVoQyxHQUFHLEVBQUU7Z0JBQ1osTUFBTWhDLFVBQVVnQyxJQUFJc0MsUUFBUTtnQkFDNUIscUJBQ0UsOERBQUNjO29CQUFJYixXQUFVOztzQ0FDYiw4REFBQ3ZJLHlEQUFNQTs0QkFBQ3lELFNBQVE7NEJBQVE0RixNQUFLOzRCQUFLQyxTQUFTLElBQU14SCxPQUFPeUgsSUFBSSxDQUFDLHVCQUFrQyxPQUFYdkYsUUFBUUgsRUFBRTs7OENBQzVGLDhEQUFDeEMsNElBQUdBO29DQUFDa0gsV0FBVTs7Ozs7O2dDQUFpQjs7Ozs7OztzQ0FHbEMsOERBQUN2SCw0RkFBb0JBOzRCQUNuQndJLFdBQVd4RixRQUFRSCxFQUFFOzRCQUNyQjRGLGFBQWEsR0FBeUJ6RixPQUF0QkEsUUFBUUMsVUFBVSxFQUFDLEtBQXFCLE9BQWxCRCxRQUFRRSxTQUFTOzRCQUN2RHdGLHVCQUNFLDhEQUFDMUoseURBQU1BO2dDQUFDeUQsU0FBUTtnQ0FBUTRGLE1BQUs7O2tEQUMzQiw4REFBQy9ILDRJQUFRQTt3Q0FBQ2lILFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7Ozs7OztzQ0FLM0MsOERBQUN0SCxzRkFBaUJBOzRCQUNoQnVJLFdBQVd4RixRQUFRSCxFQUFFOzRCQUNyQjRGLGFBQWEsR0FBeUJ6RixPQUF0QkEsUUFBUUMsVUFBVSxFQUFDLEtBQXFCLE9BQWxCRCxRQUFRRSxTQUFTOzRCQUN2RHdGLHVCQUNFLDhEQUFDMUoseURBQU1BO2dDQUFDeUQsU0FBUTtnQ0FBUTRGLE1BQUs7MENBQzNCLDRFQUFDNUgsNElBQU9BO29DQUFDOEcsV0FBVTs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJekIsOERBQUMzSCxtRkFBZ0JBOzRCQUNmb0QsU0FBU0E7NEJBQ1QyRixRQUFRakc7NEJBQ1JnRyx1QkFDRSw4REFBQzFKLHlEQUFNQTtnQ0FBQ3lELFNBQVE7Z0NBQVE0RixNQUFLOzBDQUMzQiw0RUFBQzlILDRJQUFNQTtvQ0FBQ2dILFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSXhCLDhEQUFDMUgsa0dBQXVCQTs0QkFDdEIwQyxPQUFNOzRCQUNOQyxhQUFhLG1DQUF5RFEsT0FBdEJBLFFBQVFDLFVBQVUsRUFBQyxLQUFxQixPQUFsQkQsUUFBUUUsU0FBUyxFQUFDOzRCQUN4RjBGLFdBQVcsSUFBTXpGLG9CQUFvQkgsUUFBUUgsRUFBRTs0QkFDL0M2Rix1QkFDRSw4REFBQzFKLHlEQUFNQTtnQ0FBQ3lELFNBQVE7Z0NBQVE0RixNQUFLOzBDQUMzQiw0RUFBQzNILDRJQUFNQTtvQ0FBQzZHLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7WUFNOUI7UUFDRjtLQUNEO0lBRUQsSUFBSW5HLFNBQVM7UUFDWCxxQkFDRSw4REFBQ2dIO1lBQUliLFdBQVU7c0JBQ2IsNEVBQUNhO2dCQUFJYixXQUFVOztrQ0FDYiw4REFBQzNHLDRJQUFPQTt3QkFBQzJHLFdBQVU7Ozs7OztrQ0FDbkIsOERBQUNhO3dCQUFJYixXQUFVO2tDQUFzQjs7Ozs7O2tDQUNyQyw4REFBQ2E7d0JBQUliLFdBQVU7a0NBQXFDOzs7Ozs7Ozs7Ozs7Ozs7OztJQUk1RDtJQUVBLHFCQUNFLDhEQUFDYTtRQUFJYixXQUFVOzswQkFDYiw4REFBQ2E7Z0JBQUliLFdBQVU7O2tDQUNiLDhEQUFDc0I7d0JBQUd0QixXQUFVO2tDQUFvQzs7Ozs7O2tDQUNsRCw4REFBQ2E7d0JBQUliLFdBQVU7OzBDQUNiLDhEQUFDdkkseURBQU1BO2dDQUFDeUQsU0FBUTtnQ0FBVTZGLFNBQVNuQzs7a0RBQ2pDLDhEQUFDeEYsNElBQU1BO3dDQUFDNEcsV0FBVTs7Ozs7O29DQUFpQjs7Ozs7OzswQ0FHckMsOERBQUN2SSx5REFBTUE7Z0NBQUN5RCxTQUFRO2dDQUFVNkYsU0FBU3RFOztrREFDakMsOERBQUM1RCw0SUFBUUE7d0NBQUNtSCxXQUFVOzs7Ozs7b0NBQWlCOzs7Ozs7OzBDQUd2Qyw4REFBQ3ZJLHlEQUFNQTtnQ0FBQ3lELFNBQVE7Z0NBQVU2RixTQUFTbEM7O2tEQUNqQyw4REFBQzNGLDRJQUFPQTt3Q0FBQzhHLFdBQVU7Ozs7OztvQ0FBaUI7Ozs7Ozs7MENBR3RDLDhEQUFDeEgsa0ZBQWVBO2dDQUNkK0ksT0FBT3hGO2dDQUNQb0YsdUJBQ0UsOERBQUMxSix5REFBTUE7O3NEQUNMLDhEQUFDd0IsNElBQUlBOzRDQUFDK0csV0FBVTs7Ozs7O3dDQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU8zQyw4REFBQ3RJLHFEQUFJQTs7a0NBQ0gsOERBQUNHLDJEQUFVQTtrQ0FDVCw0RUFBQ2dKOzRCQUFJYixXQUFVOzs4Q0FDYiw4REFBQ2E7O3NEQUNDLDhEQUFDL0ksMERBQVNBO3NEQUFDOzs7Ozs7c0RBQ1gsOERBQUNGLGdFQUFlQTtzREFBQzs7Ozs7Ozs7Ozs7OzhDQUVuQiw4REFBQ1csMkdBQTJCQTtvQ0FDMUJ5QyxPQUFNO29DQUNOQyxhQUFhLG1DQUF1RCxPQUFwQnRCLGFBQWE2QyxNQUFNLEVBQUM7b0NBQ3BFZ0YsT0FBTzdILGFBQWE2QyxNQUFNO29DQUMxQjZFLFdBQVdqRjs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSWpCLDhEQUFDekUsNERBQVdBO2tDQUNUa0Msd0JBQ0MsOERBQUNnSDs0QkFBSWIsV0FBVTtzQ0FDYiw0RUFBQ2E7Z0NBQUliLFdBQVU7O2tEQUNiLDhEQUFDYTt3Q0FBSWIsV0FBVTs7Ozs7O2tEQUNmLDhEQUFDeUI7d0NBQUV6QixXQUFVO2tEQUE2Qjs7Ozs7Ozs7Ozs7Ozs7OztpREFJOUMsOERBQUM1SCxnRUFBU0E7NEJBQ1IyRyxTQUFTQTs0QkFDVGxFLE1BQU1wQjs0QkFDTmlJLFdBQVU7NEJBQ1ZDLG1CQUFrQjs0QkFDbEJDLFNBQVMvQzs0QkFDVGdELFVBQVVwRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFPeEI7R0E5YXdCbkQ7O1FBQ1A5QixzREFBU0E7UUFDTm1CLHVEQUFRQTs7O0tBRkpXIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXFVzZXJcXERvY3VtZW50c1xcT1JBTkdFXFxQUk9KRUNUXFxBIEkgcHJvamVjdHNcXHNtc1xcZnJvbnRlbmRcXGFwcFxcZGFzaGJvYXJkXFxzdHVkZW50c1xccGFnZS50c3giXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcclxuXHJcbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tIFwicmVhY3RcIlxyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tIFwibmV4dC9uYXZpZ2F0aW9uXCJcclxuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9idXR0b25cIlxyXG5pbXBvcnQgeyBDYXJkLCBDYXJkQ29udGVudCwgQ2FyZERlc2NyaXB0aW9uLCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2NhcmRcIlxyXG5pbXBvcnQgeyBDaGVja2JveCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvY2hlY2tib3hcIlxyXG5pbXBvcnQgeyBBdmF0YXIsIEF2YXRhckZhbGxiYWNrLCBBdmF0YXJJbWFnZSB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvYXZhdGFyXCJcclxuaW1wb3J0IHsgQmFkZ2UgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2JhZGdlXCJcclxuaW1wb3J0IHsgRGF0YVRhYmxlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9kYXRhLXRhYmxlXCJcclxuaW1wb3J0IHR5cGUgeyBDb2x1bW5EZWYgfSBmcm9tIFwiQHRhbnN0YWNrL3JlYWN0LXRhYmxlXCJcclxuaW1wb3J0IHsgRWRpdFN0dWRlbnRNb2RhbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvbW9kYWxzL2VkaXQtc3R1ZGVudC1tb2RhbFwiXHJcbmltcG9ydCB7IERlbGV0ZUNvbmZpcm1hdGlvbk1vZGFsIH0gZnJvbSBcIkAvY29tcG9uZW50cy9tb2RhbHMvZGVsZXRlLWNvbmZpcm1hdGlvbi1tb2RhbFwiXHJcbmltcG9ydCB7IEJ1bGtEZWxldGVDb25maXJtYXRpb25Nb2RhbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvbW9kYWxzL2J1bGstZGVsZXRlLWNvbmZpcm1hdGlvbi1tb2RhbFwiXHJcbmltcG9ydCB7IEFkZFN0dWRlbnRNb2RhbCB9IGZyb20gXCJAL2NvbXBvbmVudHMvbW9kYWxzL2FkZC1zdHVkZW50LW1vZGFsXCJcclxuaW1wb3J0IHsgU3R1ZGVudERvY3VtZW50TW9kYWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL21vZGFscy9zdHVkZW50LWRvY3VtZW50LW1vZGFsXCJcclxuaW1wb3J0IHsgUHJpbnRTdHVkZW50TW9kYWwgfSBmcm9tIFwiQC9jb21wb25lbnRzL21vZGFscy9wcmludC1zdHVkZW50LW1vZGFsXCJcclxuaW1wb3J0IHsgdXNlVG9hc3QgfSBmcm9tIFwiQC9ob29rcy91c2UtdG9hc3RcIlxyXG5pbXBvcnQgeyBzdHVkZW50c0FwaSB9IGZyb20gXCJAL2xpYi9hcGlcIlxyXG5pbXBvcnQgeyBEb3dubG9hZCwgRXllLCBGaWxlVGV4dCwgUGVuY2lsLCBQbHVzLCBQcmludGVyLCBUcmFzaDIsIFVwbG9hZCwgTG9hZGVyMiB9IGZyb20gXCJsdWNpZGUtcmVhY3RcIlxyXG5cclxuaW50ZXJmYWNlIFN0dWRlbnQge1xyXG4gIGlkOiBzdHJpbmcgfCBudW1iZXJcclxuICBzdHVkZW50X2lkOiBzdHJpbmdcclxuICBmaXJzdF9uYW1lOiBzdHJpbmdcclxuICBsYXN0X25hbWU6IHN0cmluZ1xyXG4gIGVtYWlsPzogc3RyaW5nXHJcbiAgcGhvbmU/OiBzdHJpbmdcclxuICBkYXRlX29mX2JpcnRoOiBzdHJpbmdcclxuICBnZW5kZXI6IHN0cmluZ1xyXG4gIGFkZHJlc3M/OiBzdHJpbmdcclxuICBndWFyZGlhbl9uYW1lPzogc3RyaW5nXHJcbiAgZ3VhcmRpYW5fcGhvbmU/OiBzdHJpbmdcclxuICBndWFyZGlhbl9lbWFpbD86IHN0cmluZ1xyXG4gIGNsYXNzX2lkOiBzdHJpbmdcclxuICBhZG1pc3Npb25fZGF0ZTogc3RyaW5nXHJcbiAgYWRtaXNzaW9uX251bWJlcj86IHN0cmluZ1xyXG4gIHJvbGxfbnVtYmVyPzogc3RyaW5nXHJcbiAgYmxvb2RfZ3JvdXA/OiBzdHJpbmdcclxuICBuYXRpb25hbGl0eT86IHN0cmluZ1xyXG4gIHJlbGlnaW9uPzogc3RyaW5nXHJcbiAgbWVkaWNhbF9jb25kaXRpb25zPzogc3RyaW5nXHJcbiAgZW1lcmdlbmN5X2NvbnRhY3RfbmFtZT86IHN0cmluZ1xyXG4gIGVtZXJnZW5jeV9jb250YWN0X3Bob25lPzogc3RyaW5nXHJcbiAgZW1lcmdlbmN5X2NvbnRhY3RfcmVsYXRpb24/OiBzdHJpbmdcclxuICBzdGF0dXM6IHN0cmluZ1xyXG4gIGNyZWF0ZWRfYXQ6IHN0cmluZ1xyXG4gIHVwZGF0ZWRfYXQ6IHN0cmluZ1xyXG4gIGNsYXNzX25hbWU/OiBzdHJpbmdcclxuICBncmFkZV9sZXZlbD86IHN0cmluZ1xyXG4gIGFjYWRlbWljX3llYXI/OiBzdHJpbmdcclxuICBwcm9maWxlX3BpY3R1cmU/OiBzdHJpbmdcclxuICBmdWxsX25hbWU/OiBzdHJpbmdcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gU3R1ZGVudHNQYWdlKCkge1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxyXG4gIGNvbnN0IFtzdHVkZW50cywgc2V0U3R1ZGVudHNdID0gdXNlU3RhdGU8U3R1ZGVudFtdPihbXSlcclxuICBjb25zdCBbc2VsZWN0ZWRSb3dzLCBzZXRTZWxlY3RlZFJvd3NdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKVxyXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXHJcbiAgY29uc3QgW3BhZ2luYXRpb24sIHNldFBhZ2luYXRpb25dID0gdXNlU3RhdGUoe1xyXG4gICAgY3VycmVudFBhZ2U6IDEsXHJcbiAgICB0b3RhbFBhZ2VzOiAxLFxyXG4gICAgdG90YWxJdGVtczogMCxcclxuICAgIGl0ZW1zUGVyUGFnZTogMTAsXHJcbiAgfSlcclxuXHJcblxyXG5cclxuICBjb25zdCBmZXRjaFN0dWRlbnRzID0gYXN5bmMgKHBhZ2UgPSAxLCBsaW1pdCA9IDEwLCBzZWFyY2ggPSAnJykgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgc2V0TG9hZGluZyh0cnVlKVxyXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IHN0dWRlbnRzQXBpLmdldEFsbCh7XHJcbiAgICAgICAgcGFnZSxcclxuICAgICAgICBsaW1pdCxcclxuICAgICAgICBzZWFyY2gsXHJcbiAgICAgICAgc29ydF9ieTogJ2ZpcnN0X25hbWUnLFxyXG4gICAgICAgIHNvcnRfb3JkZXI6ICdBU0MnXHJcbiAgICAgIH0pXHJcblxyXG5cclxuXHJcbiAgICAgIHNldFN0dWRlbnRzKHJlc3BvbnNlLmRhdGE/LnN0dWRlbnRzIHx8IFtdKVxyXG4gICAgICBpZiAocmVzcG9uc2UuZGF0YT8ucGFnaW5hdGlvbikge1xyXG4gICAgICAgIHNldFBhZ2luYXRpb24ocmVzcG9uc2UuZGF0YS5wYWdpbmF0aW9uKVxyXG4gICAgICB9XHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBmZXRjaGluZyBzdHVkZW50czonLCBlcnJvcilcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIkVycm9yXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiRmFpbGVkIHRvIGZldGNoIHN0dWRlbnRzLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxyXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgfSlcclxuICAgIH0gZmluYWxseSB7XHJcbiAgICAgIHNldExvYWRpbmcoZmFsc2UpXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgZmV0Y2hTdHVkZW50cygpXHJcbiAgfSwgW10pXHJcblxyXG4gIGNvbnN0IGhhbmRsZVNhdmVTdHVkZW50ID0gYXN5bmMgKHVwZGF0ZWRTdHVkZW50OiBTdHVkZW50KSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCBzdHVkZW50c0FwaS51cGRhdGUodXBkYXRlZFN0dWRlbnQuaWQsIHVwZGF0ZWRTdHVkZW50KVxyXG4gICAgICBzZXRTdHVkZW50cygocHJldikgPT4gcHJldi5tYXAoKHN0dWRlbnQpID0+IChzdHVkZW50LmlkID09PSB1cGRhdGVkU3R1ZGVudC5pZCA/IHVwZGF0ZWRTdHVkZW50IDogc3R1ZGVudCkpKVxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiU3R1ZGVudCBVcGRhdGVkXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGAke3VwZGF0ZWRTdHVkZW50LmZpcnN0X25hbWV9ICR7dXBkYXRlZFN0dWRlbnQubGFzdF9uYW1lfSdzIGluZm9ybWF0aW9uIGhhcyBiZWVuIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5LmAsXHJcbiAgICAgIH0pXHJcbiAgICB9IGNhdGNoIChlcnJvcikge1xyXG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciB1cGRhdGluZyBzdHVkZW50OicsIGVycm9yKVxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gdXBkYXRlIHN0dWRlbnQuIFBsZWFzZSB0cnkgYWdhaW4uXCIsXHJcbiAgICAgICAgdmFyaWFudDogXCJkZXN0cnVjdGl2ZVwiLFxyXG4gICAgICB9KVxyXG4gICAgfVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlRGVsZXRlU3R1ZGVudCA9IGFzeW5jIChpZDogc3RyaW5nKSA9PiB7XHJcbiAgICB0cnkge1xyXG4gICAgICBhd2FpdCBzdHVkZW50c0FwaS5kZWxldGUoaWQpXHJcbiAgICAgIHNldFN0dWRlbnRzKChwcmV2KSA9PiBwcmV2LmZpbHRlcigoc3R1ZGVudCkgPT4gc3R1ZGVudC5pZCAhPT0gaWQpKVxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiU3R1ZGVudCBEZWxldGVkXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IFwiU3R1ZGVudCBoYXMgYmVlbiBkZWxldGVkIHN1Y2Nlc3NmdWxseS5cIixcclxuICAgICAgfSlcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIHN0dWRlbnQ6JywgZXJyb3IpXHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBkZWxldGUgc3R1ZGVudC4gUGxlYXNlIHRyeSBhZ2Fpbi5cIixcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgIH0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVBZGRTdHVkZW50ID0gYXN5bmMgKG5ld1N0dWRlbnQ6IGFueSkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBzdHVkZW50c0FwaS5jcmVhdGUobmV3U3R1ZGVudClcclxuXHJcbiAgICAgIC8vIFJlZnJlc2ggdGhlIHN0dWRlbnRzIGxpc3QgdG8gZ2V0IHRoZSBsYXRlc3QgZGF0YVxyXG4gICAgICBhd2FpdCBmZXRjaFN0dWRlbnRzKClcclxuXHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogXCJTdHVkZW50IEFkZGVkXCIsXHJcbiAgICAgICAgZGVzY3JpcHRpb246IGAke25ld1N0dWRlbnQuZmlyc3ROYW1lfSAke25ld1N0dWRlbnQubGFzdE5hbWV9IGhhcyBiZWVuIGFkZGVkIHN1Y2Nlc3NmdWxseS5gLFxyXG4gICAgICB9KVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgYWRkaW5nIHN0dWRlbnQ6JywgZXJyb3IpXHJcbiAgICAgIHRvYXN0KHtcclxuICAgICAgICB0aXRsZTogXCJFcnJvclwiLFxyXG4gICAgICAgIGRlc2NyaXB0aW9uOiBcIkZhaWxlZCB0byBhZGQgc3R1ZGVudC4gUGxlYXNlIHRyeSBhZ2Fpbi5cIixcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgIH0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVCdWxrRGVsZXRlID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gRGVsZXRlIHN0dWRlbnRzIG9uZSBieSBvbmUgKGNvdWxkIGJlIG9wdGltaXplZCB3aXRoIGJ1bGsgZGVsZXRlIEFQSSlcclxuICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoc2VsZWN0ZWRSb3dzLm1hcChpZCA9PiBzdHVkZW50c0FwaS5kZWxldGUoaWQpKSlcclxuICAgICAgc2V0U3R1ZGVudHMoKHByZXYpID0+IHByZXYuZmlsdGVyKChzdHVkZW50KSA9PiAhc2VsZWN0ZWRSb3dzLmluY2x1ZGVzKHN0dWRlbnQuaWQpKSlcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIlN0dWRlbnRzIERlbGV0ZWRcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogYCR7c2VsZWN0ZWRSb3dzLmxlbmd0aH0gc3R1ZGVudHMgaGF2ZSBiZWVuIGRlbGV0ZWQgc3VjY2Vzc2Z1bGx5LmAsXHJcbiAgICAgIH0pXHJcbiAgICAgIHNldFNlbGVjdGVkUm93cyhbXSlcclxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGRlbGV0aW5nIHN0dWRlbnRzOicsIGVycm9yKVxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiRXJyb3JcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gZGVsZXRlIHN0dWRlbnRzLiBQbGVhc2UgdHJ5IGFnYWluLlwiLFxyXG4gICAgICAgIHZhcmlhbnQ6IFwiZGVzdHJ1Y3RpdmVcIixcclxuICAgICAgfSlcclxuICAgIH1cclxuICB9XHJcblxyXG4gIGNvbnN0IGhhbmRsZUV4cG9ydFN0dWRlbnRzID0gYXN5bmMgKCkgPT4ge1xyXG4gICAgdHJ5IHtcclxuICAgICAgLy8gR2VuZXJhdGUgQ1NWIGV4cG9ydFxyXG4gICAgICBjb25zdCBjc3ZEYXRhID0gc3R1ZGVudHMubWFwKHN0dWRlbnQgPT4gKHtcclxuICAgICAgICAnU3R1ZGVudCBJRCc6IHN0dWRlbnQuc3R1ZGVudF9pZCxcclxuICAgICAgICAnTmFtZSc6IGAke3N0dWRlbnQuZmlyc3RfbmFtZX0gJHtzdHVkZW50Lmxhc3RfbmFtZX1gLFxyXG4gICAgICAgICdFbWFpbCc6IHN0dWRlbnQuZW1haWwgfHwgJycsXHJcbiAgICAgICAgJ1Bob25lJzogc3R1ZGVudC5waG9uZSB8fCAnJyxcclxuICAgICAgICAnR2VuZGVyJzogc3R1ZGVudC5nZW5kZXIsXHJcbiAgICAgICAgJ0RhdGUgb2YgQmlydGgnOiBzdHVkZW50LmRhdGVfb2ZfYmlydGgsXHJcbiAgICAgICAgJ0d1YXJkaWFuJzogc3R1ZGVudC5ndWFyZGlhbl9uYW1lLFxyXG4gICAgICAgICdHdWFyZGlhbiBQaG9uZSc6IHN0dWRlbnQuZ3VhcmRpYW5fcGhvbmUsXHJcbiAgICAgICAgJ0NsYXNzJzogc3R1ZGVudC5jbGFzc19uYW1lIHx8ICcnLFxyXG4gICAgICAgICdTdGF0dXMnOiBzdHVkZW50LnN0YXR1cyxcclxuICAgICAgICAnQWRtaXNzaW9uIERhdGUnOiBzdHVkZW50LmFkbWlzc2lvbl9kYXRlLFxyXG4gICAgICB9KSlcclxuXHJcbiAgICAgIGNvbnN0IGNzdkNvbnRlbnQgPSBbXHJcbiAgICAgICAgT2JqZWN0LmtleXMoY3N2RGF0YVswXSkuam9pbignLCcpLFxyXG4gICAgICAgIC4uLmNzdkRhdGEubWFwKHJvdyA9PiBPYmplY3QudmFsdWVzKHJvdykuam9pbignLCcpKVxyXG4gICAgICBdLmpvaW4oJ1xcbicpXHJcblxyXG4gICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW2NzdkNvbnRlbnRdLCB7IHR5cGU6ICd0ZXh0L2NzdicgfSlcclxuICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYilcclxuICAgICAgY29uc3QgYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2EnKVxyXG4gICAgICBhLmhyZWYgPSB1cmxcclxuICAgICAgYS5kb3dubG9hZCA9IGBzdHVkZW50c18ke25ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zcGxpdCgnVCcpWzBdfS5jc3ZgXHJcbiAgICAgIGEuY2xpY2soKVxyXG4gICAgICB3aW5kb3cuVVJMLnJldm9rZU9iamVjdFVSTCh1cmwpXHJcblxyXG4gICAgICB0b2FzdCh7XHJcbiAgICAgICAgdGl0bGU6IFwiRXhwb3J0IFN1Y2Nlc3NmdWxcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJTdHVkZW50IGRhdGEgaGFzIGJlZW4gZXhwb3J0ZWQgc3VjY2Vzc2Z1bGx5LlwiLFxyXG4gICAgICB9KVxyXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcclxuICAgICAgdG9hc3Qoe1xyXG4gICAgICAgIHRpdGxlOiBcIkV4cG9ydCBGYWlsZWRcIixcclxuICAgICAgICBkZXNjcmlwdGlvbjogXCJGYWlsZWQgdG8gZXhwb3J0IHN0dWRlbnQgZGF0YS5cIixcclxuICAgICAgICB2YXJpYW50OiBcImRlc3RydWN0aXZlXCIsXHJcbiAgICAgIH0pXHJcbiAgICB9XHJcbiAgfVxyXG5cclxuICBjb25zdCBoYW5kbGVJbXBvcnRTdHVkZW50cyA9ICgpID0+IHtcclxuICAgIHRvYXN0KHtcclxuICAgICAgdGl0bGU6IFwiSW1wb3J0IEZlYXR1cmVcIixcclxuICAgICAgZGVzY3JpcHRpb246IFwiU3R1ZGVudCBpbXBvcnQgZnVuY3Rpb25hbGl0eSB3aWxsIGJlIGltcGxlbWVudGVkIHdpdGggZmlsZSB1cGxvYWQuXCIsXHJcbiAgICB9KVxyXG4gIH1cclxuXHJcbiAgY29uc3QgaGFuZGxlUHJpbnRBbGwgPSAoKSA9PiB7XHJcbiAgICB3aW5kb3cucHJpbnQoKVxyXG4gICAgdG9hc3Qoe1xyXG4gICAgICB0aXRsZTogXCJQcmludCBTdGFydGVkXCIsXHJcbiAgICAgIGRlc2NyaXB0aW9uOiBcIlByZXBhcmluZyBzdHVkZW50IGxpc3QgZm9yIHByaW50aW5nLlwiLFxyXG4gICAgfSlcclxuICB9XHJcblxyXG4gIGNvbnN0IGNvbHVtbnM6IENvbHVtbkRlZjxTdHVkZW50PltdID0gW1xyXG4gICAge1xyXG4gICAgICBpZDogXCJzZWxlY3RcIixcclxuICAgICAgaGVhZGVyOiAoeyB0YWJsZSB9KSA9PiAoXHJcbiAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICBjaGVja2VkPXt0YWJsZS5nZXRJc0FsbFBhZ2VSb3dzU2VsZWN0ZWQoKSB8fCAodGFibGUuZ2V0SXNTb21lUGFnZVJvd3NTZWxlY3RlZCgpICYmIFwiaW5kZXRlcm1pbmF0ZVwiKX1cclxuICAgICAgICAgIG9uQ2hlY2tlZENoYW5nZT17KHZhbHVlKSA9PiB0YWJsZS50b2dnbGVBbGxQYWdlUm93c1NlbGVjdGVkKCEhdmFsdWUpfVxyXG4gICAgICAgICAgYXJpYS1sYWJlbD1cIlNlbGVjdCBhbGxcIlxyXG4gICAgICAgIC8+XHJcbiAgICAgICksXHJcbiAgICAgIGNlbGw6ICh7IHJvdyB9KSA9PiAoXHJcbiAgICAgICAgPENoZWNrYm94XHJcbiAgICAgICAgICBjaGVja2VkPXtyb3cuZ2V0SXNTZWxlY3RlZCgpfVxyXG4gICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsodmFsdWUpID0+IHJvdy50b2dnbGVTZWxlY3RlZCghIXZhbHVlKX1cclxuICAgICAgICAgIGFyaWEtbGFiZWw9XCJTZWxlY3Qgcm93XCJcclxuICAgICAgICAvPlxyXG4gICAgICApLFxyXG4gICAgICBlbmFibGVTb3J0aW5nOiBmYWxzZSxcclxuICAgICAgZW5hYmxlSGlkaW5nOiBmYWxzZSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGFjY2Vzc29yS2V5OiBcInN0dWRlbnRfaWRcIixcclxuICAgICAgaGVhZGVyOiBcIlN0dWRlbnQgSURcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGFjY2Vzc29yS2V5OiBcInBob3RvXCIsXHJcbiAgICAgIGhlYWRlcjogXCJQaG90b1wiLFxyXG4gICAgICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHN0dWRlbnQgPSByb3cub3JpZ2luYWxcclxuICAgICAgICByZXR1cm4gKFxyXG4gICAgICAgICAgPEF2YXRhciBjbGFzc05hbWU9XCJoLTEwIHctMTBcIj5cclxuICAgICAgICAgICAge3N0dWRlbnQucHJvZmlsZV9waWN0dXJlID8gKFxyXG4gICAgICAgICAgICAgIDxBdmF0YXJJbWFnZSBzcmM9e3N0dWRlbnQucHJvZmlsZV9waWN0dXJlIHx8IFwiL3BsYWNlaG9sZGVyLnN2Z1wifSBhbHQ9e2Ake3N0dWRlbnQuZmlyc3RfbmFtZX0gJHtzdHVkZW50Lmxhc3RfbmFtZX1gfSAvPlxyXG4gICAgICAgICAgICApIDogKFxyXG4gICAgICAgICAgICAgIDxBdmF0YXJGYWxsYmFjaz57c3R1ZGVudC5maXJzdF9uYW1lLmNoYXJBdCgwKX17c3R1ZGVudC5sYXN0X25hbWUuY2hhckF0KDApfTwvQXZhdGFyRmFsbGJhY2s+XHJcbiAgICAgICAgICAgICl9XHJcbiAgICAgICAgICA8L0F2YXRhcj5cclxuICAgICAgICApXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBhY2Nlc3NvcktleTogXCJmaXJzdF9uYW1lXCIsXHJcbiAgICAgIGhlYWRlcjogXCJOYW1lXCIsXHJcbiAgICAgIGNlbGw6ICh7IHJvdyB9KSA9PiB7XHJcbiAgICAgICAgY29uc3Qgc3R1ZGVudCA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgIHJldHVybiBgJHtzdHVkZW50LmZpcnN0X25hbWV9ICR7c3R1ZGVudC5sYXN0X25hbWV9YFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgYWNjZXNzb3JLZXk6IFwiY2xhc3NfbmFtZVwiLFxyXG4gICAgICBoZWFkZXI6IFwiQ2xhc3NcIixcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGFjY2Vzc29yS2V5OiBcImVtYWlsXCIsXHJcbiAgICAgIGhlYWRlcjogXCJFbWFpbFwiLFxyXG4gICAgICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGVtYWlsID0gcm93LmdldFZhbHVlKFwiZW1haWxcIikgYXMgc3RyaW5nXHJcbiAgICAgICAgcmV0dXJuIGVtYWlsIHx8IFwiTi9BXCJcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGFjY2Vzc29yS2V5OiBcInBob25lXCIsXHJcbiAgICAgIGhlYWRlcjogXCJQaG9uZVwiLFxyXG4gICAgICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHBob25lID0gcm93LmdldFZhbHVlKFwicGhvbmVcIikgYXMgc3RyaW5nXHJcbiAgICAgICAgcmV0dXJuIHBob25lIHx8IFwiTi9BXCJcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGFjY2Vzc29yS2V5OiBcImdlbmRlclwiLFxyXG4gICAgICBoZWFkZXI6IFwiR2VuZGVyXCIsXHJcbiAgICAgIGNlbGw6ICh7IHJvdyB9KSA9PiB7XHJcbiAgICAgICAgY29uc3QgZ2VuZGVyID0gcm93LmdldFZhbHVlKFwiZ2VuZGVyXCIpIGFzIHN0cmluZ1xyXG4gICAgICAgIHJldHVybiBnZW5kZXIgPyBnZW5kZXIuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyBnZW5kZXIuc2xpY2UoMSkgOiBcIk4vQVwiXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBhY2Nlc3NvcktleTogXCJkYXRlX29mX2JpcnRoXCIsXHJcbiAgICAgIGhlYWRlcjogXCJEYXRlIG9mIEJpcnRoXCIsXHJcbiAgICAgIGNlbGw6ICh7IHJvdyB9KSA9PiB7XHJcbiAgICAgICAgY29uc3QgZG9iID0gcm93LmdldFZhbHVlKFwiZGF0ZV9vZl9iaXJ0aFwiKSBhcyBzdHJpbmdcclxuICAgICAgICByZXR1cm4gZG9iID8gbmV3IERhdGUoZG9iKS50b0xvY2FsZURhdGVTdHJpbmcoKSA6IFwiTi9BXCJcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGFjY2Vzc29yS2V5OiBcImFkbWlzc2lvbl9kYXRlXCIsXHJcbiAgICAgIGhlYWRlcjogXCJBZG1pc3Npb24gRGF0ZVwiLFxyXG4gICAgICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGFkbWlzc2lvbkRhdGUgPSByb3cuZ2V0VmFsdWUoXCJhZG1pc3Npb25fZGF0ZVwiKSBhcyBzdHJpbmdcclxuICAgICAgICByZXR1cm4gYWRtaXNzaW9uRGF0ZSA/IG5ldyBEYXRlKGFkbWlzc2lvbkRhdGUpLnRvTG9jYWxlRGF0ZVN0cmluZygpIDogXCJOL0FcIlxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgYWNjZXNzb3JLZXk6IFwiZ3VhcmRpYW5fbmFtZVwiLFxyXG4gICAgICBoZWFkZXI6IFwiUGFyZW50L0d1YXJkaWFuXCIsXHJcbiAgICAgIGNlbGw6ICh7IHJvdyB9KSA9PiB7XHJcbiAgICAgICAgY29uc3QgZ3VhcmRpYW4gPSByb3cuZ2V0VmFsdWUoXCJndWFyZGlhbl9uYW1lXCIpIGFzIHN0cmluZ1xyXG4gICAgICAgIHJldHVybiBndWFyZGlhbiB8fCBcIk4vQVwiXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBhY2Nlc3NvcktleTogXCJndWFyZGlhbl9waG9uZVwiLFxyXG4gICAgICBoZWFkZXI6IFwiR3VhcmRpYW4gQ29udGFjdFwiLFxyXG4gICAgICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IGd1YXJkaWFuUGhvbmUgPSByb3cuZ2V0VmFsdWUoXCJndWFyZGlhbl9waG9uZVwiKSBhcyBzdHJpbmdcclxuICAgICAgICByZXR1cm4gZ3VhcmRpYW5QaG9uZSB8fCBcIk4vQVwiXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBhY2Nlc3NvcktleTogXCJzdGF0dXNcIixcclxuICAgICAgaGVhZGVyOiBcIlN0YXR1c1wiLFxyXG4gICAgICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgICAgIGNvbnN0IHN0YXR1cyA9IHJvdy5nZXRWYWx1ZShcInN0YXR1c1wiKSBhcyBzdHJpbmdcclxuICAgICAgICByZXR1cm4gPEJhZGdlIHZhcmlhbnQ9e3N0YXR1cyA9PT0gXCJhY3RpdmVcIiA/IFwiZGVmYXVsdFwiIDogXCJvdXRsaW5lXCJ9PntzdGF0dXN9PC9CYWRnZT5cclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGlkOiBcImFjdGlvbnNcIixcclxuICAgICAgY2VsbDogKHsgcm93IH0pID0+IHtcclxuICAgICAgICBjb25zdCBzdHVkZW50ID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBqdXN0aWZ5LWVuZCBnYXAtMlwiPlxyXG4gICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiIG9uQ2xpY2s9eygpID0+IHJvdXRlci5wdXNoKGAvZGFzaGJvYXJkL3N0dWRlbnRzLyR7c3R1ZGVudC5pZH1gKX0+XHJcbiAgICAgICAgICAgICAgPEV5ZSBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgIERldGFpbHNcclxuICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDxTdHVkZW50RG9jdW1lbnRNb2RhbFxyXG4gICAgICAgICAgICAgIHN0dWRlbnRJZD17c3R1ZGVudC5pZH1cclxuICAgICAgICAgICAgICBzdHVkZW50TmFtZT17YCR7c3R1ZGVudC5maXJzdF9uYW1lfSAke3N0dWRlbnQubGFzdF9uYW1lfWB9XHJcbiAgICAgICAgICAgICAgdHJpZ2dlcj17XHJcbiAgICAgICAgICAgICAgICA8QnV0dG9uIHZhcmlhbnQ9XCJnaG9zdFwiIHNpemU9XCJzbVwiPlxyXG4gICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgICAgRG9jc1xyXG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgICA8UHJpbnRTdHVkZW50TW9kYWxcclxuICAgICAgICAgICAgICBzdHVkZW50SWQ9e3N0dWRlbnQuaWR9XHJcbiAgICAgICAgICAgICAgc3R1ZGVudE5hbWU9e2Ake3N0dWRlbnQuZmlyc3RfbmFtZX0gJHtzdHVkZW50Lmxhc3RfbmFtZX1gfVxyXG4gICAgICAgICAgICAgIHRyaWdnZXI9e1xyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgPFByaW50ZXIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIDxFZGl0U3R1ZGVudE1vZGFsXHJcbiAgICAgICAgICAgICAgc3R1ZGVudD17c3R1ZGVudH1cclxuICAgICAgICAgICAgICBvblNhdmU9e2hhbmRsZVNhdmVTdHVkZW50fVxyXG4gICAgICAgICAgICAgIHRyaWdnZXI9e1xyXG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwic21cIj5cclxuICAgICAgICAgICAgICAgICAgPFBlbmNpbCBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgPERlbGV0ZUNvbmZpcm1hdGlvbk1vZGFsXHJcbiAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgU3R1ZGVudFwiXHJcbiAgICAgICAgICAgICAgZGVzY3JpcHRpb249e2BBcmUgeW91IHN1cmUgeW91IHdhbnQgdG8gZGVsZXRlICR7c3R1ZGVudC5maXJzdF9uYW1lfSAke3N0dWRlbnQubGFzdF9uYW1lfT8gVGhpcyBhY3Rpb24gY2Fubm90IGJlIHVuZG9uZS5gfVxyXG4gICAgICAgICAgICAgIG9uQ29uZmlybT17KCkgPT4gaGFuZGxlRGVsZXRlU3R1ZGVudChzdHVkZW50LmlkKX1cclxuICAgICAgICAgICAgICB0cmlnZ2VyPXtcclxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCI+XHJcbiAgICAgICAgICAgICAgICAgIDxUcmFzaDIgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XHJcbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gIF1cclxuXHJcbiAgaWYgKGxvYWRpbmcpIHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLVs1MHZoXSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICA8TG9hZGVyMiBjbGFzc05hbWU9XCJoLTggdy04IGFuaW1hdGUtc3BpbiBteC1hdXRvIG1iLTRcIiAvPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWxnIGZvbnQtbWVkaXVtXCI+TG9hZGluZyBzdHVkZW50cy4uLjwvZGl2PlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+UGxlYXNlIHdhaXQgd2hpbGUgd2UgZmV0Y2ggdGhlIHN0dWRlbnQgZGF0YS48L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICApXHJcbiAgfVxyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtY29sIGdhcC00XCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuXCI+XHJcbiAgICAgICAgPGgyIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0cmFja2luZy10aWdodFwiPlN0dWRlbnRzPC9oMj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZ2FwLTJcIj5cclxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXtoYW5kbGVJbXBvcnRTdHVkZW50c30+XHJcbiAgICAgICAgICAgIDxVcGxvYWQgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgSW1wb3J0XHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cIm91dGxpbmVcIiBvbkNsaWNrPXtoYW5kbGVFeHBvcnRTdHVkZW50c30+XHJcbiAgICAgICAgICAgIDxEb3dubG9hZCBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICBFeHBvcnRcclxuICAgICAgICAgIDwvQnV0dG9uPlxyXG4gICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiIG9uQ2xpY2s9e2hhbmRsZVByaW50QWxsfT5cclxuICAgICAgICAgICAgPFByaW50ZXIgY2xhc3NOYW1lPVwibXItMiBoLTQgdy00XCIgLz5cclxuICAgICAgICAgICAgUHJpbnQgQWxsXHJcbiAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgIDxBZGRTdHVkZW50TW9kYWxcclxuICAgICAgICAgICAgb25BZGQ9e2hhbmRsZUFkZFN0dWRlbnR9XHJcbiAgICAgICAgICAgIHRyaWdnZXI9e1xyXG4gICAgICAgICAgICAgIDxCdXR0b24+XHJcbiAgICAgICAgICAgICAgICA8UGx1cyBjbGFzc05hbWU9XCJtci0yIGgtNCB3LTRcIiAvPlxyXG4gICAgICAgICAgICAgICAgQWRkIFN0dWRlbnRcclxuICAgICAgICAgICAgICA8L0J1dHRvbj5cclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICAgIDxDYXJkPlxyXG4gICAgICAgIDxDYXJkSGVhZGVyPlxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cclxuICAgICAgICAgICAgPGRpdj5cclxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlPlN0dWRlbnQgTWFuYWdlbWVudDwvQ2FyZFRpdGxlPlxyXG4gICAgICAgICAgICAgIDxDYXJkRGVzY3JpcHRpb24+VmlldyBhbmQgbWFuYWdlIGFsbCBzdHVkZW50cyBpbiB0aGUgc3lzdGVtPC9DYXJkRGVzY3JpcHRpb24+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8QnVsa0RlbGV0ZUNvbmZpcm1hdGlvbk1vZGFsXHJcbiAgICAgICAgICAgICAgdGl0bGU9XCJEZWxldGUgU2VsZWN0ZWQgU3R1ZGVudHNcIlxyXG4gICAgICAgICAgICAgIGRlc2NyaXB0aW9uPXtgQXJlIHlvdSBzdXJlIHlvdSB3YW50IHRvIGRlbGV0ZSAke3NlbGVjdGVkUm93cy5sZW5ndGh9IHNlbGVjdGVkIHN0dWRlbnRzPyBUaGlzIGFjdGlvbiBjYW5ub3QgYmUgdW5kb25lLmB9XHJcbiAgICAgICAgICAgICAgY291bnQ9e3NlbGVjdGVkUm93cy5sZW5ndGh9XHJcbiAgICAgICAgICAgICAgb25Db25maXJtPXtoYW5kbGVCdWxrRGVsZXRlfVxyXG4gICAgICAgICAgICAvPlxyXG4gICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgPC9DYXJkSGVhZGVyPlxyXG4gICAgICAgIDxDYXJkQ29udGVudD5cclxuICAgICAgICAgIHtsb2FkaW5nID8gKFxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGgtMzJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC04IHctOCBib3JkZXItYi0yIGJvcmRlci1ncmF5LTkwMCBteC1hdXRvXCI+PC9kaXY+XHJcbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJtdC0yIHRleHQtc20gdGV4dC1ncmF5LTYwMFwiPkxvYWRpbmcgc3R1ZGVudHMuLi48L3A+XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgPERhdGFUYWJsZVxyXG4gICAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgICAgZGF0YT17c3R1ZGVudHN9XHJcbiAgICAgICAgICAgICAgc2VhcmNoS2V5PVwiZmlyc3RfbmFtZVwiXHJcbiAgICAgICAgICAgICAgc2VhcmNoUGxhY2Vob2xkZXI9XCJTZWFyY2ggc3R1ZGVudHMuLi5cIlxyXG4gICAgICAgICAgICAgIG9uUHJpbnQ9e2hhbmRsZVByaW50QWxsfVxyXG4gICAgICAgICAgICAgIG9uRXhwb3J0PXtoYW5kbGVFeHBvcnRTdHVkZW50c31cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICAgICl9XHJcbiAgICAgICAgPC9DYXJkQ29udGVudD5cclxuICAgICAgPC9DYXJkPlxyXG4gICAgPC9kaXY+XHJcbiAgKVxyXG59XHJcbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsInVzZVJvdXRlciIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmREZXNjcmlwdGlvbiIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJDaGVja2JveCIsIkF2YXRhciIsIkF2YXRhckZhbGxiYWNrIiwiQXZhdGFySW1hZ2UiLCJCYWRnZSIsIkRhdGFUYWJsZSIsIkVkaXRTdHVkZW50TW9kYWwiLCJEZWxldGVDb25maXJtYXRpb25Nb2RhbCIsIkJ1bGtEZWxldGVDb25maXJtYXRpb25Nb2RhbCIsIkFkZFN0dWRlbnRNb2RhbCIsIlN0dWRlbnREb2N1bWVudE1vZGFsIiwiUHJpbnRTdHVkZW50TW9kYWwiLCJ1c2VUb2FzdCIsInN0dWRlbnRzQXBpIiwiRG93bmxvYWQiLCJFeWUiLCJGaWxlVGV4dCIsIlBlbmNpbCIsIlBsdXMiLCJQcmludGVyIiwiVHJhc2gyIiwiVXBsb2FkIiwiTG9hZGVyMiIsIlN0dWRlbnRzUGFnZSIsInJvdXRlciIsInRvYXN0Iiwic3R1ZGVudHMiLCJzZXRTdHVkZW50cyIsInNlbGVjdGVkUm93cyIsInNldFNlbGVjdGVkUm93cyIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwicGFnaW5hdGlvbiIsInNldFBhZ2luYXRpb24iLCJjdXJyZW50UGFnZSIsInRvdGFsUGFnZXMiLCJ0b3RhbEl0ZW1zIiwiaXRlbXNQZXJQYWdlIiwiZmV0Y2hTdHVkZW50cyIsInBhZ2UiLCJsaW1pdCIsInNlYXJjaCIsInJlc3BvbnNlIiwiZ2V0QWxsIiwic29ydF9ieSIsInNvcnRfb3JkZXIiLCJkYXRhIiwiZXJyb3IiLCJjb25zb2xlIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInZhcmlhbnQiLCJoYW5kbGVTYXZlU3R1ZGVudCIsInVwZGF0ZWRTdHVkZW50IiwidXBkYXRlIiwiaWQiLCJwcmV2IiwibWFwIiwic3R1ZGVudCIsImZpcnN0X25hbWUiLCJsYXN0X25hbWUiLCJoYW5kbGVEZWxldGVTdHVkZW50IiwiZGVsZXRlIiwiZmlsdGVyIiwiaGFuZGxlQWRkU3R1ZGVudCIsIm5ld1N0dWRlbnQiLCJjcmVhdGUiLCJmaXJzdE5hbWUiLCJsYXN0TmFtZSIsImhhbmRsZUJ1bGtEZWxldGUiLCJQcm9taXNlIiwiYWxsIiwiaW5jbHVkZXMiLCJsZW5ndGgiLCJoYW5kbGVFeHBvcnRTdHVkZW50cyIsImNzdkRhdGEiLCJzdHVkZW50X2lkIiwiZW1haWwiLCJwaG9uZSIsImdlbmRlciIsImRhdGVfb2ZfYmlydGgiLCJndWFyZGlhbl9uYW1lIiwiZ3VhcmRpYW5fcGhvbmUiLCJjbGFzc19uYW1lIiwic3RhdHVzIiwiYWRtaXNzaW9uX2RhdGUiLCJjc3ZDb250ZW50IiwiT2JqZWN0Iiwia2V5cyIsImpvaW4iLCJyb3ciLCJ2YWx1ZXMiLCJibG9iIiwiQmxvYiIsInR5cGUiLCJ1cmwiLCJ3aW5kb3ciLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJhIiwiZG9jdW1lbnQiLCJjcmVhdGVFbGVtZW50IiwiaHJlZiIsImRvd25sb2FkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwic3BsaXQiLCJjbGljayIsInJldm9rZU9iamVjdFVSTCIsImhhbmRsZUltcG9ydFN0dWRlbnRzIiwiaGFuZGxlUHJpbnRBbGwiLCJwcmludCIsImNvbHVtbnMiLCJoZWFkZXIiLCJ0YWJsZSIsImNoZWNrZWQiLCJnZXRJc0FsbFBhZ2VSb3dzU2VsZWN0ZWQiLCJnZXRJc1NvbWVQYWdlUm93c1NlbGVjdGVkIiwib25DaGVja2VkQ2hhbmdlIiwidmFsdWUiLCJ0b2dnbGVBbGxQYWdlUm93c1NlbGVjdGVkIiwiYXJpYS1sYWJlbCIsImNlbGwiLCJnZXRJc1NlbGVjdGVkIiwidG9nZ2xlU2VsZWN0ZWQiLCJlbmFibGVTb3J0aW5nIiwiZW5hYmxlSGlkaW5nIiwiYWNjZXNzb3JLZXkiLCJvcmlnaW5hbCIsImNsYXNzTmFtZSIsInByb2ZpbGVfcGljdHVyZSIsInNyYyIsImFsdCIsImNoYXJBdCIsImdldFZhbHVlIiwidG9VcHBlckNhc2UiLCJzbGljZSIsImRvYiIsInRvTG9jYWxlRGF0ZVN0cmluZyIsImFkbWlzc2lvbkRhdGUiLCJndWFyZGlhbiIsImd1YXJkaWFuUGhvbmUiLCJkaXYiLCJzaXplIiwib25DbGljayIsInB1c2giLCJzdHVkZW50SWQiLCJzdHVkZW50TmFtZSIsInRyaWdnZXIiLCJvblNhdmUiLCJvbkNvbmZpcm0iLCJoMiIsIm9uQWRkIiwiY291bnQiLCJwIiwic2VhcmNoS2V5Iiwic2VhcmNoUGxhY2Vob2xkZXIiLCJvblByaW50Iiwib25FeHBvcnQiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});