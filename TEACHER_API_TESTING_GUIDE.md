# Teacher Dashboard API Testing Guide

## 🧪 Testing Overview

This guide provides comprehensive testing instructions for all teacher dashboard API endpoints. Each endpoint includes sample requests, expected responses, and error scenarios.

## 🔐 Authentication Setup

### Prerequisites
1. **JWT Token**: Obtain a valid JWT token for a teacher user
2. **Base URL**: `http://localhost:5000/api`
3. **Headers**: Include `Authorization: Bearer <jwt_token>` in all requests

### Sample Authentication Header
```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

## 📊 API Endpoint Tests

### 1. Teacher Dashboard Overview

#### **GET /api/teacher-portal/dashboard**

**Purpose**: Get comprehensive dashboard data for the authenticated teacher

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/dashboard" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Teacher dashboard data retrieved successfully",
  "data": {
    "teacher": {
      "id": 1,
      "teacher_id": "TCH-20240001",
      "first_name": "<PERSON>",
      "last_name": "<PERSON>",
      "department": "Mathematics",
      "designation": "Senior Teacher",
      "academic_year": "2024-2025"
    },
    "statistics": {
      "total_classes": 3,
      "total_subjects": 2,
      "total_students": 85,
      "pending_assessments": 2
    },
    "attendance_today": {
      "total_marked": 85,
      "present_count": 78,
      "absent_count": 7,
      "attendance_rate": 91.76
    },
    "recent_lesson_notes": [
      {
        "id": 1,
        "title": "Algebra Basics",
        "lesson_date": "2024-01-15",
        "subject_name": "Mathematics",
        "class_name": "Grade 10-A"
      }
    ],
    "upcoming_events": [],
    "unread_messages": 3
  }
}
```

### 2. Teacher Profile Management

#### **GET /api/teacher-portal/profile**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/profile" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Teacher profile retrieved successfully",
  "data": {
    "id": 1,
    "teacher_id": "TCH-20240001",
    "employee_id": "EMP-001",
    "first_name": "John",
    "last_name": "Smith",
    "email": "<EMAIL>",
    "phone": "+1234567890",
    "department": "Mathematics",
    "designation": "Senior Teacher",
    "qualification": "M.Sc. Mathematics, B.Ed.",
    "experience_years": 8,
    "joining_date": "2020-06-01",
    "emergency_contact_name": "Jane Smith",
    "emergency_contact_phone": "+0987654321"
  }
}
```

#### **PUT /api/teacher-portal/profile**

**Sample Request**:
```bash
curl -X PUT "http://localhost:5000/api/teacher-portal/profile" \
  -H "Authorization: Bearer <jwt_token>" \
  -H "Content-Type: application/json" \
  -d '{
    "phone": "+1234567890",
    "address": "123 Teacher Lane, Education City",
    "emergency_contact_name": "Jane Smith",
    "emergency_contact_phone": "+0987654321",
    "emergency_contact_relation": "Spouse",
    "qualification": "M.Sc. Mathematics, B.Ed., Ph.D. (pursuing)",
    "specialization": "Advanced Calculus, Statistics"
  }'
```

### 3. Teacher Classes

#### **GET /api/teacher-portal/classes**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/classes?page=1&limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Teacher classes retrieved successfully",
  "data": [
    {
      "id": 1,
      "uuid": "class-uuid-123",
      "name": "Grade 10-A",
      "grade_level": "10",
      "section": "A",
      "capacity": 30,
      "student_count": 28,
      "room_number": "101",
      "academic_year": "2024-2025",
      "role": "class_teacher"
    },
    {
      "id": 2,
      "uuid": "class-uuid-456",
      "name": "Grade 11-B",
      "grade_level": "11",
      "section": "B",
      "capacity": 32,
      "student_count": 30,
      "room_number": "205",
      "academic_year": "2024-2025",
      "role": "subject_teacher",
      "subjects": "Mathematics,Statistics"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 1,
    "totalItems": 2,
    "itemsPerPage": 10,
    "hasNextPage": false,
    "hasPrevPage": false
  }
}
```

### 4. Teacher Subjects

#### **GET /api/teacher-portal/subjects**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/subjects" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Teacher subjects retrieved successfully",
  "data": [
    {
      "id": 1,
      "uuid": "subject-uuid-123",
      "name": "Mathematics",
      "code": "MATH101",
      "description": "Basic Mathematics for Grade 10",
      "department": "Mathematics",
      "credit_hours": 4,
      "is_mandatory": true,
      "classes_count": 3,
      "class_names": "Grade 10-A,Grade 10-B,Grade 11-A"
    }
  ]
}
```

### 5. Teacher Students

#### **GET /api/teacher-portal/students**

**Sample Request with Filters**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/students?page=1&limit=10&search=john&status=active" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Students retrieved successfully",
  "data": [
    {
      "id": 1,
      "student_id": "STU-20240001",
      "roll_number": "001",
      "admission_number": "ADM-2024-001",
      "first_name": "John",
      "last_name": "Doe",
      "email": "<EMAIL>",
      "phone": "+1111111111",
      "class_name": "Grade 10-A",
      "grade_level": "10",
      "section": "A",
      "status": "active",
      "admission_date": "2024-01-01"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 5,
    "totalItems": 45,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### 6. Teacher Timetable

#### **GET /api/teacher-portal/timetable**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/timetable" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Teacher timetable retrieved successfully",
  "data": {
    "periods": {
      "Monday": [
        {
          "period_number": 1,
          "start_time": "08:00:00",
          "end_time": "08:45:00",
          "subject_name": "Mathematics",
          "subject_code": "MATH101",
          "class_name": "Grade 10-A",
          "grade_level": "10",
          "section": "A",
          "room_number": "101"
        }
      ],
      "Tuesday": [
        {
          "period_number": 2,
          "start_time": "09:00:00",
          "end_time": "09:45:00",
          "subject_name": "Statistics",
          "subject_code": "STAT101",
          "class_name": "Grade 11-B",
          "grade_level": "11",
          "section": "B",
          "room_number": "205"
        }
      ]
    },
    "total_periods": 15
  }
}
```

### 7. Teacher Attendance Records

#### **GET /api/teacher-portal/attendance**

**Sample Request with Filters**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/attendance?page=1&limit=10&date_from=2024-01-01&date_to=2024-01-31" \
  -H "Authorization: Bearer <jwt_token>"
```

**Expected Response**:
```json
{
  "success": true,
  "message": "Attendance records retrieved successfully",
  "data": [
    {
      "id": 1,
      "attendance_date": "2024-01-15",
      "period_number": 1,
      "status": "present",
      "check_in_time": "08:00:00",
      "student_name": "John Doe",
      "student_id": "STU-20240001",
      "roll_number": "001",
      "class_name": "Grade 10-A",
      "grade_level": "10",
      "subject_name": "Mathematics",
      "subject_code": "MATH101"
    }
  ],
  "pagination": {
    "currentPage": 1,
    "totalPages": 10,
    "totalItems": 100,
    "itemsPerPage": 10,
    "hasNextPage": true,
    "hasPrevPage": false
  },
  "statistics": {
    "total_records": 100,
    "present_count": 85,
    "absent_count": 12,
    "late_count": 3,
    "attendance_rate": 85.0
  }
}
```

### 8. Teacher Assessments

#### **GET /api/teacher-portal/assessments**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/assessments?page=1&limit=10&status=published" \
  -H "Authorization: Bearer <jwt_token>"
```

### 9. Teacher Lesson Notes

#### **GET /api/teacher-portal/lesson-notes**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/lesson-notes?page=1&limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

### 10. Teacher Results

#### **GET /api/teacher-portal/results**

**Sample Request**:
```bash
curl -X GET "http://localhost:5000/api/teacher-portal/results?page=1&limit=10" \
  -H "Authorization: Bearer <jwt_token>"
```

## ❌ Error Scenarios

### 1. Authentication Errors

**Missing Token**:
```json
{
  "success": false,
  "message": "Access token required"
}
```

**Invalid Token**:
```json
{
  "success": false,
  "message": "Invalid or expired token"
}
```

**Wrong User Type**:
```json
{
  "success": false,
  "message": "Access denied. Teacher role required."
}
```

### 2. Validation Errors

**Invalid Query Parameters**:
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "page",
      "message": "Page must be a positive integer"
    }
  ]
}
```

### 3. Resource Access Errors

**Teacher Record Not Found**:
```json
{
  "success": false,
  "message": "Teacher record not found"
}
```

**Unauthorized Access**:
```json
{
  "success": false,
  "message": "Access denied. You can only access your assigned classes."
}
```

## 🧪 Testing Checklist

### Functional Testing
- [ ] All endpoints return correct data for authenticated teacher
- [ ] Pagination works correctly on all paginated endpoints
- [ ] Filtering parameters work as expected
- [ ] Profile update only allows permitted fields
- [ ] Access control prevents unauthorized data access
- [ ] Statistics calculations are accurate

### Security Testing
- [ ] Endpoints reject requests without authentication
- [ ] Teachers cannot access other teachers' data
- [ ] Teachers can only access their assigned classes/subjects
- [ ] Input validation prevents malicious input
- [ ] SQL injection attempts are blocked
- [ ] Rate limiting works correctly

### Performance Testing
- [ ] Response times are under 500ms for simple queries
- [ ] Large datasets are properly paginated
- [ ] Database queries are optimized
- [ ] Memory usage is reasonable
- [ ] Complex JOIN queries perform well

### Access Control Testing
- [ ] Class teachers can access their class data
- [ ] Subject teachers can access their subject data
- [ ] Combined role teachers get appropriate access
- [ ] Student data is properly scoped
- [ ] Assessment data is limited to teacher's assessments

This comprehensive testing guide ensures all teacher dashboard API endpoints are thoroughly tested for functionality, security, and performance while maintaining proper access control.
