"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/students/page",{

/***/ "(app-pages-browser)/./app/dashboard/students/page.tsx":
/*!*****************************************!*\
  !*** ./app/dashboard/students/page.tsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentsPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/checkbox */ \"(app-pages-browser)/./components/ui/checkbox.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/data-table */ \"(app-pages-browser)/./components/ui/data-table.tsx\");\n/* harmony import */ var _components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/modals/edit-student-modal */ \"(app-pages-browser)/./components/modals/edit-student-modal.tsx\");\n/* harmony import */ var _components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/modals/delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/modals/bulk-delete-confirmation-modal */ \"(app-pages-browser)/./components/modals/bulk-delete-confirmation-modal.tsx\");\n/* harmony import */ var _components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/modals/add-student-modal */ \"(app-pages-browser)/./components/modals/add-student-modal.tsx\");\n/* harmony import */ var _components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/modals/student-document-modal */ \"(app-pages-browser)/./components/modals/student-document-modal.tsx\");\n/* harmony import */ var _components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/modals/print-student-modal */ \"(app-pages-browser)/./components/modals/print-student-modal.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./src/lib/api.ts\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/printer.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pencil.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Download,Eye,FileText,Loader2,Pencil,Plus,Printer,Trash2,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction StudentsPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast)();\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedRows, setSelectedRows] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        currentPage: 1,\n        totalPages: 1,\n        totalItems: 0,\n        itemsPerPage: 10\n    });\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1, limit = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 10, search = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '';\n        try {\n            var _response_data, _response_data1;\n            setLoading(true);\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.getAll({\n                page,\n                limit,\n                search,\n                sort_by: 'first_name',\n                sort_order: 'ASC'\n            });\n            setStudents(((_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.students) || []);\n            if ((_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.pagination) {\n                setPagination(response.data.pagination);\n            }\n        } catch (error) {\n            console.error('Error fetching students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to fetch students. Please try again.\",\n                variant: \"destructive\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentsPage.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentsPage.useEffect\"], []);\n    const handleSaveStudent = async (updatedStudent)=>{\n        try {\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.update(updatedStudent.id, updatedStudent);\n            setStudents((prev)=>prev.map((student)=>student.id === updatedStudent.id ? updatedStudent : student));\n            toast({\n                title: \"Student Updated\",\n                description: \"\".concat(updatedStudent.first_name, \" \").concat(updatedStudent.last_name, \"'s information has been updated successfully.\")\n            });\n        } catch (error) {\n            console.error('Error updating student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to update student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteStudent = async (id)=>{\n        try {\n            const stringId = String(id);\n            await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(stringId);\n            setStudents((prev)=>prev.filter((student)=>String(student.id) !== stringId));\n            toast({\n                title: \"Student Deleted\",\n                description: \"Student has been deleted successfully.\"\n            });\n        } catch (error) {\n            console.error('Error deleting student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleAddStudent = async (newStudent)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.create(newStudent);\n            // Refresh the students list to get the latest data\n            await fetchStudents();\n            toast({\n                title: \"Student Added\",\n                description: \"\".concat(newStudent.firstName, \" \").concat(newStudent.lastName, \" has been added successfully.\")\n            });\n        } catch (error) {\n            console.error('Error adding student:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to add student. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleBulkDelete = async ()=>{\n        try {\n            // Delete students one by one (could be optimized with bulk delete API)\n            await Promise.all(selectedRows.map((id)=>_lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.delete(String(id))));\n            setStudents((prev)=>prev.filter((student)=>!selectedRows.includes(String(student.id))));\n            toast({\n                title: \"Students Deleted\",\n                description: \"\".concat(selectedRows.length, \" students have been deleted successfully.\")\n            });\n            setSelectedRows([]);\n        } catch (error) {\n            console.error('Error deleting students:', error);\n            toast({\n                title: \"Error\",\n                description: \"Failed to delete students. Please try again.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleExportStudents = async ()=>{\n        try {\n            if (students.length === 0) {\n                toast({\n                    title: \"No Data\",\n                    description: \"No students to export.\",\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            // Generate CSV export\n            const csvData = students.map((student)=>({\n                    'Student ID': student.student_id || '',\n                    'Name': \"\".concat(student.first_name, \" \").concat(student.last_name),\n                    'Email': student.email || '',\n                    'Phone': student.phone || '',\n                    'Gender': student.gender || '',\n                    'Date of Birth': student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : '',\n                    'Blood Group': student.blood_group || '',\n                    'Nationality': student.nationality || '',\n                    'Religion': student.religion || '',\n                    'Address': student.address || '',\n                    'Emergency Contact': student.emergency_contact_name || '',\n                    'Emergency Phone': student.emergency_contact_phone || '',\n                    'Class': student.class_name || '',\n                    'Grade Level': student.grade_level || '',\n                    'Academic Year': student.academic_year || '',\n                    'Status': student.user_status || '',\n                    'Admission Date': student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '',\n                    'Admission Number': student.admission_number || '',\n                    'Roll Number': student.roll_number || '',\n                    'Medical Conditions': student.medical_conditions || '',\n                    'Transport Required': student.transport_required ? 'Yes' : 'No',\n                    'Hostel Required': student.hostel_required ? 'Yes' : 'No'\n                }));\n            // Helper function to escape CSV values\n            const escapeCSV = (value)=>{\n                if (value === null || value === undefined) return '';\n                const str = String(value);\n                if (str.includes(',') || str.includes('\"') || str.includes('\\n')) {\n                    return '\"'.concat(str.replace(/\"/g, '\"\"'), '\"');\n                }\n                return str;\n            };\n            const csvContent = [\n                Object.keys(csvData[0]).join(','),\n                ...csvData.map((row)=>Object.values(row).map(escapeCSV).join(','))\n            ].join('\\n');\n            const blob = new Blob([\n                csvContent\n            ], {\n                type: 'text/csv'\n            });\n            const url = window.URL.createObjectURL(blob);\n            const a = document.createElement('a');\n            a.href = url;\n            a.download = \"students_\".concat(new Date().toISOString().split('T')[0], \".csv\");\n            a.click();\n            window.URL.revokeObjectURL(url);\n            toast({\n                title: \"Export Successful\",\n                description: \"Student data has been exported successfully.\"\n            });\n        } catch (error) {\n            toast({\n                title: \"Export Failed\",\n                description: \"Failed to export student data.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleImportStudents = ()=>{\n        // Create a file input element\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.accept = '.csv';\n        input.onchange = async (e)=>{\n            var _e_target_files;\n            const file = (_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0];\n            if (!file) return;\n            try {\n                const text = await file.text();\n                const lines = text.split('\\n').filter((line)=>line.trim());\n                if (lines.length < 2) {\n                    toast({\n                        title: \"Invalid File\",\n                        description: \"CSV file must have at least a header and one data row.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Parse CSV (basic implementation)\n                const headers = lines[0].split(',').map((h)=>h.trim());\n                const requiredHeaders = [\n                    'firstName',\n                    'lastName',\n                    'email',\n                    'dateOfBirth',\n                    'gender'\n                ];\n                const missingHeaders = requiredHeaders.filter((h)=>!headers.includes(h));\n                if (missingHeaders.length > 0) {\n                    toast({\n                        title: \"Invalid CSV Format\",\n                        description: \"Missing required columns: \".concat(missingHeaders.join(', ')),\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                const students = lines.slice(1).map((line)=>{\n                    const values = line.split(',').map((v)=>v.trim().replace(/^\"|\"$/g, ''));\n                    const student = {};\n                    headers.forEach((header, index)=>{\n                        student[header] = values[index] || '';\n                    });\n                    return student;\n                });\n                // Validate and import students\n                const validStudents = students.filter((student)=>student.firstName && student.lastName && student.email && student.dateOfBirth && student.gender);\n                if (validStudents.length === 0) {\n                    toast({\n                        title: \"No Valid Students\",\n                        description: \"No valid student records found in the CSV file.\",\n                        variant: \"destructive\"\n                    });\n                    return;\n                }\n                // Use bulk create API\n                await _lib_api__WEBPACK_IMPORTED_MODULE_16__.studentsApi.bulkCreate(validStudents);\n                toast({\n                    title: \"Import Successful\",\n                    description: \"Successfully imported \".concat(validStudents.length, \" students.\")\n                });\n                // Refresh the students list\n                await fetchStudents();\n            } catch (error) {\n                console.error('Import error:', error);\n                toast({\n                    title: \"Import Failed\",\n                    description: \"Failed to import students. Please check the file format.\",\n                    variant: \"destructive\"\n                });\n            }\n        };\n        input.click();\n    };\n    const handlePrintAll = ()=>{\n        if (students.length === 0) {\n            toast({\n                title: \"No Data\",\n                description: \"No students to print.\",\n                variant: \"destructive\"\n            });\n            return;\n        }\n        // Create a new window for printing\n        const printWindow = window.open('', '_blank');\n        if (!printWindow) return;\n        const printContent = '\\n      <!DOCTYPE html>\\n      <html>\\n        <head>\\n          <title>Students List</title>\\n          <style>\\n            body {\\n              font-family: Arial, sans-serif;\\n              margin: 20px;\\n              line-height: 1.4;\\n            }\\n            .header {\\n              text-align: center;\\n              border-bottom: 2px solid #333;\\n              padding-bottom: 20px;\\n              margin-bottom: 30px;\\n            }\\n            table {\\n              width: 100%;\\n              border-collapse: collapse;\\n              margin-top: 20px;\\n            }\\n            th, td {\\n              border: 1px solid #ddd;\\n              padding: 8px;\\n              text-align: left;\\n              font-size: 12px;\\n            }\\n            th {\\n              background-color: #f5f5f5;\\n              font-weight: bold;\\n            }\\n            .footer {\\n              margin-top: 30px;\\n              text-align: center;\\n              font-size: 10px;\\n              color: #666;\\n            }\\n            @media print {\\n              body { margin: 0; }\\n              .no-print { display: none; }\\n            }\\n          </style>\\n        </head>\\n        <body>\\n          <div class=\"header\">\\n            <h1>Students List</h1>\\n            <p>Total Students: '.concat(students.length, \"</p>\\n            <p>Generated on: \").concat(new Date().toLocaleDateString(), \"</p>\\n          </div>\\n\\n          <table>\\n            <thead>\\n              <tr>\\n                <th>Student ID</th>\\n                <th>Name</th>\\n                <th>Email</th>\\n                <th>Phone</th>\\n                <th>Class</th>\\n                <th>Status</th>\\n                <th>Admission Date</th>\\n              </tr>\\n            </thead>\\n            <tbody>\\n              \").concat(students.map((student)=>\"\\n                <tr>\\n                  <td>\".concat(student.student_id || '', \"</td>\\n                  <td>\").concat(student.first_name, \" \").concat(student.last_name, \"</td>\\n                  <td>\").concat(student.email || '', \"</td>\\n                  <td>\").concat(student.phone || '', \"</td>\\n                  <td>\").concat(student.class_name || '', \"</td>\\n                  <td>\").concat(student.user_status || '', \"</td>\\n                  <td>\").concat(student.admission_date ? new Date(student.admission_date).toLocaleDateString() : '', \"</td>\\n                </tr>\\n              \")).join(''), '\\n            </tbody>\\n          </table>\\n\\n          <div class=\"footer\">\\n            <p>School Management System - Students Report</p>\\n          </div>\\n        </body>\\n      </html>\\n    ');\n        printWindow.document.write(printContent);\n        printWindow.document.close();\n        printWindow.focus();\n        // Wait for content to load then print\n        setTimeout(()=>{\n            printWindow.print();\n            printWindow.close();\n        }, 250);\n        toast({\n            title: \"Print Started\",\n            description: \"Preparing student list for printing.\"\n        });\n    };\n    const columns = [\n        {\n            id: \"select\",\n            header: (param)=>{\n                let { table } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: table.getIsAllPageRowsSelected() || table.getIsSomePageRowsSelected() && \"indeterminate\",\n                    onCheckedChange: (value)=>table.toggleAllPageRowsSelected(!!value),\n                    \"aria-label\": \"Select all\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 475,\n                    columnNumber: 9\n                }, this);\n            },\n            cell: (param)=>{\n                let { row } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_checkbox__WEBPACK_IMPORTED_MODULE_5__.Checkbox, {\n                    checked: row.getIsSelected(),\n                    onCheckedChange: (value)=>row.toggleSelected(!!value),\n                    \"aria-label\": \"Select row\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 482,\n                    columnNumber: 9\n                }, this);\n            },\n            enableSorting: false,\n            enableHiding: false\n        },\n        {\n            accessorKey: \"student_id\",\n            header: \"Student ID\"\n        },\n        {\n            accessorKey: \"photo\",\n            header: \"Photo\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.Avatar, {\n                    className: \"h-10 w-10\",\n                    children: student.profile_picture ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarImage, {\n                        src: student.profile_picture || \"/placeholder.svg\",\n                        alt: \"\".concat(student.first_name, \" \").concat(student.last_name)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 503,\n                        columnNumber: 15\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_6__.AvatarFallback, {\n                        children: [\n                            student.first_name.charAt(0),\n                            student.last_name.charAt(0)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 505,\n                        columnNumber: 15\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 501,\n                    columnNumber: 11\n                }, this);\n            }\n        },\n        {\n            accessorKey: \"first_name\",\n            header: \"Name\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return \"\".concat(student.first_name, \" \").concat(student.last_name);\n            }\n        },\n        {\n            accessorKey: \"class_name\",\n            header: \"Class\"\n        },\n        {\n            accessorKey: \"email\",\n            header: \"Email\",\n            cell: (param)=>{\n                let { row } = param;\n                const email = row.getValue(\"email\");\n                return email || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"phone\",\n            header: \"Phone\",\n            cell: (param)=>{\n                let { row } = param;\n                const phone = row.getValue(\"phone\");\n                return phone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"gender\",\n            header: \"Gender\",\n            cell: (param)=>{\n                let { row } = param;\n                const gender = row.getValue(\"gender\");\n                return gender ? gender.charAt(0).toUpperCase() + gender.slice(1) : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"date_of_birth\",\n            header: \"Date of Birth\",\n            cell: (param)=>{\n                let { row } = param;\n                const dob = row.getValue(\"date_of_birth\");\n                return dob ? new Date(dob).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"admission_date\",\n            header: \"Admission Date\",\n            cell: (param)=>{\n                let { row } = param;\n                const admissionDate = row.getValue(\"admission_date\");\n                return admissionDate ? new Date(admissionDate).toLocaleDateString() : \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_name\",\n            header: \"Parent/Guardian\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardian = row.getValue(\"guardian_name\");\n                return guardian || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"guardian_phone\",\n            header: \"Guardian Contact\",\n            cell: (param)=>{\n                let { row } = param;\n                const guardianPhone = row.getValue(\"guardian_phone\");\n                return guardianPhone || \"N/A\";\n            }\n        },\n        {\n            accessorKey: \"user_status\",\n            header: \"Status\",\n            cell: (param)=>{\n                let { row } = param;\n                const status = row.getValue(\"user_status\");\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    variant: status === \"active\" ? \"default\" : \"outline\",\n                    children: status || \"N/A\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 16\n                }, this);\n            }\n        },\n        {\n            id: \"actions\",\n            cell: (param)=>{\n                let { row } = param;\n                const student = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"sm\",\n                            onClick: ()=>router.push(\"/dashboard/students/\".concat(student.id)),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    className: \"mr-2 h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 594,\n                                    columnNumber: 15\n                                }, this),\n                                \"Details\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_student_document_modal__WEBPACK_IMPORTED_MODULE_13__.StudentDocumentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 19\n                                    }, void 0),\n                                    \"Docs\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 601,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 597,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_print_student_modal__WEBPACK_IMPORTED_MODULE_14__.PrintStudentModal, {\n                            studentId: student.id,\n                            studentName: \"\".concat(student.first_name, \" \").concat(student.last_name),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 607,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_edit_student_modal__WEBPACK_IMPORTED_MODULE_9__.EditStudentModal, {\n                            student: student,\n                            onSave: handleSaveStudent,\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 616,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_10__.DeleteConfirmationModal, {\n                            title: \"Delete Student\",\n                            description: \"Are you sure you want to delete \".concat(student.first_name, \" \").concat(student.last_name, \"? This action cannot be undone.\"),\n                            onConfirm: ()=>handleDeleteStudent(student.id),\n                            trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 631,\n                                    columnNumber: 19\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 630,\n                                columnNumber: 17\n                            }, void 0)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 625,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                    lineNumber: 592,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n    ];\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex h-[50vh] items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 645,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-lg font-medium\",\n                        children: \"Loading students...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 646,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-2 text-sm text-muted-foreground\",\n                        children: \"Please wait while we fetch the student data.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 647,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 644,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n            lineNumber: 643,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col gap-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-bold tracking-tight\",\n                        children: \"Students\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 656,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleImportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 659,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Import\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handleExportStudents,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Export\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 662,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"outline\",\n                                onClick: handlePrintAll,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 667,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Print All\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_add_student_modal__WEBPACK_IMPORTED_MODULE_12__.AddStudentModal, {\n                                onAdd: handleAddStudent,\n                                trigger: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Download_Eye_FileText_Loader2_Pencil_Plus_Printer_Trash2_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 674,\n                                            columnNumber: 17\n                                        }, void 0),\n                                        \"Add Student\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 15\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 670,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 655,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            children: \"Student Management\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 685,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                            children: \"View and manage all students in the system\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                            lineNumber: 686,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_modals_bulk_delete_confirmation_modal__WEBPACK_IMPORTED_MODULE_11__.BulkDeleteConfirmationModal, {\n                                    title: \"Delete Selected Students\",\n                                    description: \"Are you sure you want to delete \".concat(selectedRows.length, \" selected students? This action cannot be undone.\"),\n                                    count: selectedRows.length,\n                                    onConfirm: handleBulkDelete\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                    lineNumber: 688,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 683,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 682,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center h-32\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"mt-2 text-sm text-gray-600\",\n                                        children: \"Loading students...\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                        lineNumber: 701,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                                lineNumber: 699,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 698,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_data_table__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                            columns: columns,\n                            data: students,\n                            searchKey: \"first_name\",\n                            searchPlaceholder: \"Search students...\",\n                            onPrint: handlePrintAll,\n                            onExport: handleExportStudents\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                            lineNumber: 705,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                        lineNumber: 696,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n                lineNumber: 681,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\ORANGE\\\\PROJECT\\\\A I projects\\\\sms\\\\frontend\\\\app\\\\dashboard\\\\students\\\\page.tsx\",\n        lineNumber: 654,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentsPage, \"2mPRm9Jx2cynZ5f7wAONxa8oUKE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_15__.useToast\n    ];\n});\n_c = StudentsPage;\nvar _c;\n$RefreshReg$(_c, \"StudentsPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/students/page.tsx\n"));

/***/ })

});